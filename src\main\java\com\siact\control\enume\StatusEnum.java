package com.siact.control.enume;

/**
 * <AUTHOR>
 * @Package com.siact.wjycontrol.enume
 * @description: 命令状态枚举类
 * @create 2023/6/13 14:25
 */
public enum StatusEnum {
    COMM_EXECUTING(1, "运行中"),
    COMM_EXECUTED(2, "运行结束"),
    COMM_CANCEL(0, "已弃用");
    /** 状态码 */
    private Integer resultCode;

    /** 状态描述 */
    private String resultMsg;

    StatusEnum(Integer resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }
    public int code() {
        return resultCode;
    }

    public String message() {
        return resultMsg;
    }

}
