package com.siact.control.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.control.entity.CommandDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 17:14:20
 */
@Mapper
public interface CommandDetailDao extends BaseMapper<CommandDetailEntity> {

    @Update("update ct_command_detail set command_status=#{commandStatus},end_flag=#{endFlag} where id=#{commandId}")
    boolean updateByCommandId(Long commandId, String commandStatus,Integer endFlag);

    @Select("select * from ct_command_detail where dev_property=#{executeId}")
    CommandDetailEntity getDeviceModeByPropCode(String executeId);
}
