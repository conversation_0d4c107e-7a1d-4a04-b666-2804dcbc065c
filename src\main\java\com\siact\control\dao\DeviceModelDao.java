package com.siact.control.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.control.entity.DeviceModelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-09 16:16:57
 */
@Mapper
public interface DeviceModelDao extends BaseMapper<DeviceModelEntity> {
    @Select("select * from ct_device_model where dev_property=#{propCode}")
    DeviceModelEntity getDeviceModelByPropCode(String propCode);

    @Select("select * from ct_device_model where dev_property is not null")
    List<DeviceModelEntity> getAllModelByDevProperty();
    @Select("select * from ct_device_model where prop_code is not null")
    List<DeviceModelEntity> getAllModelByPropCode();
}
