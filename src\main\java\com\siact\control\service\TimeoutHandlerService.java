package com.siact.control.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.enume.TimeoutTypeEnum;
import com.siact.control.exception.ErrorCode;
import com.siact.control.utils.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 超时处理服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class TimeoutHandlerService {

    @Autowired
    private CommandDetailService commandDetailService;

    @Value("${mqtt.controlTimeout:2}")
    private int controlTimeout;

    @Value("${mqtt.readTimeout:2}")
    private int readTimeout;

    @Value("${mqtt.maxRetryCount:3}")
    private int maxRetryCount;

    /**
     * 处理控制命令超时
     * @param commandId 命令ID
     * @param gatewayCode 网关编码
     * @param retryCount 重试次数
     * @return 处理结果
     */
    public JSONArray handleControlTimeout(Long commandId, String gatewayCode, int retryCount) {
        log.error("控制命令超时 - 命令ID: {}, 网关: {}, 重试次数: {}/{}", 
                commandId, gatewayCode, retryCount, maxRetryCount);

        JSONArray resultArray = new JSONArray();
        
        // 获取该命令在该网关下的所有点位
        List<String> itemIdList = TaskUtil.getItemsByCommandIdAndGateway(commandId, gatewayCode);
        
        if (itemIdList.isEmpty()) {
            log.warn("未找到命令ID {} 在网关 {} 下的点位信息", commandId, gatewayCode);
            return resultArray;
        }

        // 构建超时响应结果
        JSONObject timeoutResult = new JSONObject();
        timeoutResult.put("gatewayCode", gatewayCode);
        timeoutResult.put("commandId", commandId);
        timeoutResult.put("timeoutType", TimeoutTypeEnum.CONTROL_TIMEOUT.getCode());
        timeoutResult.put("retryCount", retryCount);
        
        // 将所有点位状态设置为失败
        String cmdState = itemIdList.stream()
                .map(itemId -> itemId + ":False")
                .collect(Collectors.joining("/"));
        timeoutResult.put("cmdstate", cmdState);
        timeoutResult.put("msg", "控制命令执行超时，设备状态未知");
        
        // 更新数据库命令状态
        updateCommandStatus(commandId, "TIMEOUT", 
                String.format("网关 %s 控制超时，重试次数: %d", gatewayCode, retryCount));
        
        // 清理该网关的映射关系
        TaskUtil.removeCommandGatewayItemsByGateway(commandId, gatewayCode);
        
        resultArray.add(timeoutResult);
        return resultArray;
    }

    /**
     * 处理数据读取超时
     * @param gatewayCode 网关编码
     * @param requestedItemIds 请求的点位ID列表
     * @param retryCount 重试次数
     * @return 处理结果
     */
    public List<DataCodeValDTO> handleReadTimeout(String gatewayCode, List<String> requestedItemIds, int retryCount) {
        log.warn("数据读取超时 - 网关: {}, 重试次数: {}/{}", gatewayCode, retryCount, maxRetryCount);
        
        List<DataCodeValDTO> resultList = new ArrayList<>();
        
        // 对于读取超时，返回空数据或默认值
        if (requestedItemIds != null) {
            for (String itemId : requestedItemIds) {
                String propCode = TaskUtil.iotReadPropMap.get(gatewayCode + "_" + itemId);
                if (propCode != null) {
                    DataCodeValDTO timeoutData = new DataCodeValDTO();
                    timeoutData.setDataCode(propCode);
                    timeoutData.setVal("TIMEOUT"); // 标记为超时
                    resultList.add(timeoutData);
                    
                    // 清理映射关系
                    TaskUtil.iotReadPropMap.remove(gatewayCode + "_" + itemId);
                }
            }
        }
        
        return resultList;
    }

    /**
     * 判断是否需要重试
     * @param retryCount 当前重试次数
     * @param timeoutType 超时类型
     * @return 是否需要重试
     */
    public boolean shouldRetry(int retryCount, TimeoutTypeEnum timeoutType) {
        if (retryCount >= maxRetryCount) {
            return false;
        }
        
        // 控制命令更严格，减少重试次数
        if (timeoutType == TimeoutTypeEnum.CONTROL_TIMEOUT) {
            return retryCount < Math.min(maxRetryCount, 2);
        }
        
        // 读取命令可以多重试几次
        return retryCount < maxRetryCount;
    }

    /**
     * 获取重试延迟时间（毫秒）
     * @param retryCount 重试次数
     * @param timeoutType 超时类型
     * @return 延迟时间
     */
    public long getRetryDelay(int retryCount, TimeoutTypeEnum timeoutType) {
        // 指数退避策略
        long baseDelay = timeoutType == TimeoutTypeEnum.CONTROL_TIMEOUT ? 1000 : 500;
        return baseDelay * (long) Math.pow(2, retryCount);
    }

    /**
     * 更新命令状态
     */
    private void updateCommandStatus(Long commandId, String status, String message) {
        try {
            commandDetailService.updateByCommandId(commandId, 
                    String.format("{\"status\":\"%s\",\"message\":\"%s\",\"timestamp\":%d}", 
                            status, message, System.currentTimeMillis()), 0);
        } catch (Exception e) {
            log.error("更新命令状态失败 - 命令ID: {}", commandId, e);
        }
    }

    /**
     * 创建超时响应消息
     * @param messageId 消息ID
     * @param gatewayCode 网关编码
     * @param timeoutType 超时类型
     * @return 超时响应消息
     */
    public String createTimeoutResponse(Long messageId, String gatewayCode, TimeoutTypeEnum timeoutType) {
        JSONObject response = new JSONObject();
        response.put("code", ErrorCode.REQUEST_TIMEOUT.getCode());
        response.put("msg", timeoutType.getMessage());
        response.put("messageId", messageId);
        response.put("gatewayCode", gatewayCode);
        response.put("timeoutType", timeoutType.getCode());
        response.put("timestamp", System.currentTimeMillis());
        response.put("data", null);
        
        return response.toJSONString();
    }
}
