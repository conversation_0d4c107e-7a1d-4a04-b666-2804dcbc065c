package com.siact.control.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.enume.TimeoutTypeEnum;
import com.siact.control.exception.ErrorCode;
import com.siact.control.utils.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 超时处理服务 - 工业控制专用（无重试机制）
 * 控制领域原则：超时就是失败，不允许重试
 * <AUTHOR>
 */
@Slf4j
@Service
public class TimeoutHandlerService {

    @Autowired
    private CommandDetailService commandDetailService;

    @Value("${mqtt.controlTimeout:2}")
    private int controlTimeout;

    @Value("${mqtt.readTimeout:2}")
    private int readTimeout;

    /**
     * 处理控制命令超时 - 简化处理，直接返回超时错误
     * @param commandId 命令ID
     * @param gatewayCode 网关编码
     * @return 处理结果
     */
    public JSONArray handleControlTimeout(Long commandId, String gatewayCode) {
        log.error("控制命令超时 - 命令ID: {}, 网关: {} [直接返回超时错误]",
                commandId, gatewayCode);

        JSONArray resultArray = new JSONArray();

        // 获取该命令在该网关下的所有点位
        List<String> itemIdList = TaskUtil.getItemsByCommandIdAndGateway(commandId, gatewayCode);

        if (itemIdList.isEmpty()) {
            log.warn("未找到命令ID {} 在网关 {} 下的点位信息", commandId, gatewayCode);
            return resultArray;
        }

        // 构建超时响应结果 - 简化处理
        JSONObject timeoutResult = new JSONObject();
        timeoutResult.put("gatewayCode", gatewayCode);
        timeoutResult.put("commandId", commandId);
        timeoutResult.put("timeoutType", TimeoutTypeEnum.CONTROL_TIMEOUT.getCode());
        timeoutResult.put("timestamp", System.currentTimeMillis());

        // 简化处理：将该网关下的点位状态设置为超时失败
        String cmdState = itemIdList.stream()
                .map(itemId -> itemId + ":Timeout")
                .collect(Collectors.joining("/"));
        timeoutResult.put("cmdstate", cmdState);
        timeoutResult.put("msg", String.format("网关 %s 控制超时", gatewayCode));
        timeoutResult.put("success", false);

        // 更新数据库命令状态
        updateCommandStatus(commandId, "GATEWAY_TIMEOUT",
                String.format("网关 %s 控制超时", gatewayCode));

        // 清理该网关的映射关系
        TaskUtil.removeCommandGatewayItemsByGateway(commandId, gatewayCode);

        resultArray.add(timeoutResult);
        return resultArray;
    }

    /**
     * 处理数据读取超时 - 读取操作可以部分失败
     * @param gatewayCode 网关编码
     * @param requestedItemIds 请求的点位ID列表
     * @return 处理结果（空列表，表示该网关数据不可用）
     */
    public List<DataCodeValDTO> handleReadTimeout(String gatewayCode, List<String> requestedItemIds) {
        log.warn("数据读取超时 - 网关: {} [读取领域：该网关数据不返回，其他网关正常]", gatewayCode);

        // 读取超时：直接返回空列表，不影响其他网关的数据读取
        List<DataCodeValDTO> resultList = new ArrayList<>();

        // 清理该网关相关的读取映射关系
        if (requestedItemIds != null) {
            for (String itemId : requestedItemIds) {
                TaskUtil.iotReadPropMap.remove(gatewayCode + "_" + itemId);
            }
        } else {
            // 如果没有具体的itemId列表，清理该网关下所有的读取映射
            TaskUtil.iotReadPropMap.entrySet().removeIf(entry ->
                entry.getKey().startsWith(gatewayCode + "_"));
        }

        log.info("网关 {} 读取超时，已清理映射关系，该网关数据将不返回", gatewayCode);
        return resultList; // 返回空列表
    }

    /**
     * 判断是否有网关超时 - 用于直接返回错误
     * @param timeoutGateways 超时的网关列表
     * @param totalGateways 总网关数量
     * @return 是否有超时网关
     */
    public boolean hasTimeoutGateways(List<String> timeoutGateways, int totalGateways) {
        boolean hasTimeout = !timeoutGateways.isEmpty();

        if (hasTimeout) {
            log.error("检测到网关超时 - 超时网关: {}, 总网关数: {}",
                    timeoutGateways, totalGateways);
        }

        return hasTimeout;
    }

    /**
     * 判断读取操作是否应该继续
     * 读取领域：部分网关超时不影响其他网关的数据返回
     * @param timeoutGateways 超时的网关列表
     * @param totalGateways 总网关数量
     * @return 是否应该继续返回其他网关的数据
     */
    public boolean shouldReadOperationContinue(List<String> timeoutGateways, int totalGateways) {
        // 读取领域：即使有网关超时，其他网关的数据仍然可以返回
        boolean shouldContinue = timeoutGateways.size() < totalGateways;

        if (!timeoutGateways.isEmpty()) {
            log.warn("读取操作部分失败 - 超时网关: {}, 成功网关数: {}, 将返回可用数据",
                    timeoutGateways, totalGateways - timeoutGateways.size());
        }

        return shouldContinue;
    }

    /**
     * 更新命令状态
     */
    private void updateCommandStatus(Long commandId, String status, String message) {
        try {
            commandDetailService.updateByCommandId(commandId, 
                    String.format("{\"status\":\"%s\",\"message\":\"%s\",\"timestamp\":%d}", 
                            status, message, System.currentTimeMillis()), 0);
        } catch (Exception e) {
            log.error("更新命令状态失败 - 命令ID: {}", commandId, e);
        }
    }

    /**
     * 创建超时响应消息
     * @param messageId 消息ID
     * @param gatewayCode 网关编码
     * @param timeoutType 超时类型
     * @return 超时响应消息
     */
    public String createTimeoutResponse(Long messageId, String gatewayCode, TimeoutTypeEnum timeoutType) {
        JSONObject response = new JSONObject();
        response.put("code", ErrorCode.REQUEST_TIMEOUT.getCode());
        response.put("msg", timeoutType.getMessage());
        response.put("messageId", messageId);
        response.put("gatewayCode", gatewayCode);
        response.put("timeoutType", timeoutType.getCode());
        response.put("timestamp", System.currentTimeMillis());
        response.put("data", null);
        
        return response.toJSONString();
    }
}
