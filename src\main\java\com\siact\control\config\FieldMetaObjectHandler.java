package com.siact.control.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * mybatis-plus 自动填充字段
 */
public class FieldMetaObjectHandler implements MetaObjectHandler {
	private final static String CREATE_TIME = "issueTime";
	private final static String UPDATE_TIME = "updateTime";


	@Override
	public void insertFill(MetaObject metaObject) {
	/*	UserDetail user = SecurityUser.getUser();
		if (user.getId() != null) {
			// 创建者
			setFieldValByName(CREATOR, user.getId(), metaObject);
			// 更新者
			setFieldValByName(UPDATER, user.getId(), metaObject);
			// 创建者所属机构
			setFieldValByName(ORG_ID, user.getOrgId(), metaObject);
		}*/
		// 创建时间
		setFieldValByName(CREATE_TIME, new Date(), metaObject);
		// 更新时间
		setFieldValByName(UPDATE_TIME, new Date(), metaObject);
	}

	@Override
	public void updateFill(MetaObject metaObject) {
		/*Long userId = SecurityUser.getUserId();
		if (userId != null) {
			// 更新者
			setFieldValByName(UPDATER, userId, metaObject);
		}*/
		// 更新时间
		setFieldValByName(UPDATE_TIME, new Date(), metaObject);
	}
}
