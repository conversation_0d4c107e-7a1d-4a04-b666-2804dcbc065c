package com.siact.control.config;

import com.github.xiaoymin.knife4j.spring.extension.OpenApiExtensionResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * SwaggerConfig
 *
 * <AUTHOR>
 * @since 2024-05-13 16:07:00
 */
@Configuration
public class SwaggerConfig {

    @Value("${siact.env:dev}")
    private String env;
    @Value("${knife4j.host:#{null}}")
    private String host;
    @Value("${knife4j.termsOfServiceUrl:#{null}}")
    private String termsOfServiceUrl;
    @Value("${knife4j.description:#{null}}")
    private String description;
    @Value("${knife4j.apiVersion:1.0}")
    private String apiVersion;
    @Value("${knife4j.apiTitle:MEOS操作系统控制接口}")
    private String apiTitle;

    private final static String ENV_DEV = "dev";
    private final static String ENV_PRO = "pro";

    private final OpenApiExtensionResolver openApiExtensionResolver;

    @Autowired
    public SwaggerConfig(OpenApiExtensionResolver openApiExtensionResolver) {
        this.openApiExtensionResolver = openApiExtensionResolver;
    }

    @Bean
    public Docket api() {
        Docket build = new Docket(DocumentationType.SWAGGER_2);
        if (StringUtils.hasLength(host)) {
            build = build.host(host);
        }
        build = build.apiInfo(apiInfo())
                .pathMapping("/")
                .select() // 选择那些路径和api会生成document
                .apis(RequestHandlerSelectors.any())// 对所有api进行监控
                .build()
                .extensions(openApiExtensionResolver.buildSettingExtensions());
        if (ENV_PRO.equals(env)) {
            build = build.select().paths(PathSelectors.regex("/public/.*")).build();
        } else {
            //不显示错误的接口地址
            build = build.select().paths(PathSelectors.regex("(?!/error.*).*")).build();// 对根下所有路径进行监控
        }
        return build;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .description(description)
                .termsOfServiceUrl(termsOfServiceUrl)
                .version(apiVersion)
                .title(apiTitle)
                .build();
    }
}
