# 网关响应超时处理改进方案

## 🎯 问题分析

### 原有问题
1. **处理策略单一**: 读取和控制操作使用相同的超时处理逻辑
2. **缺乏重试机制**: 网络抖动导致的临时超时没有重试
3. **状态不明确**: 控制命令超时后设备实际状态未知
4. **资源清理不完整**: 超时后相关映射关系清理不彻底

## 💡 改进方案

### 1. 差异化超时处理策略

#### 控制命令超时处理
```java
// 特点：更严格的处理，需要明确设备状态
- 重试次数较少（最多2次）
- 超时后将所有点位状态设置为 False
- 更新数据库命令状态为 TIMEOUT
- 清理相关映射关系
- 记录详细的超时信息
```

#### 数据读取超时处理
```java
// 特点：相对宽松，可以接受部分数据丢失
- 重试次数较多（最多3次）
- 超时后返回 TIMEOUT 标记
- 不影响设备状态
- 清理读取映射关系
```

### 2. 新增组件

#### TimeoutTypeEnum - 超时类型枚举
```java
public enum TimeoutTypeEnum {
    CONTROL_TIMEOUT(1, "控制命令超时"),
    READ_TIMEOUT(2, "数据读取超时"),
    NETWORK_TIMEOUT(3, "网络连接超时");
}
```

#### TimeoutHandlerService - 超时处理服务
```java
@Service
public class TimeoutHandlerService {
    // 处理控制命令超时
    public JSONArray handleControlTimeout(Long commandId, String gatewayCode, int retryCount)
    
    // 处理数据读取超时
    public List<DataCodeValDTO> handleReadTimeout(String gatewayCode, List<String> requestedItemIds, int retryCount)
    
    // 判断是否需要重试
    public boolean shouldRetry(int retryCount, TimeoutTypeEnum timeoutType)
    
    // 获取重试延迟时间（指数退避）
    public long getRetryDelay(int retryCount, TimeoutTypeEnum timeoutType)
}
```

#### RetryService - 重试服务
```java
@Service
public class RetryService {
    // 异步重试控制命令
    @Async
    public CompletableFuture<Result> retryControlCommand(Long commandId, String gatewayCode, JSONObject mqttCommand, int retryCount)
    
    // 异步重试读取命令
    @Async
    public CompletableFuture<List<DataCodeValDTO>> retryReadCommand(String gatewayCode, JSONObject mqttCommand, int retryCount)
    
    // 批量重试控制命令
    @Async
    public CompletableFuture<Result> batchRetryControlCommands(Map<String, JSONObject> commandMap, int retryCount)
}
```

### 3. 配置参数

```yaml
mqtt:
  # 控制命令超时时间（秒）
  controlTimeout: 2
  # 读取实时数据超时时间（秒）
  readTimeout: 2
  # 最大重试次数
  maxRetryCount: 3
  # 重试间隔基数（毫秒）
  retryBaseDelay: 1000
```

### 4. 重试策略

#### 指数退避算法
```java
// 重试延迟 = 基础延迟 * 2^重试次数
long delay = baseDelay * (long) Math.pow(2, retryCount);

// 示例：
// 第1次重试：1000ms
// 第2次重试：2000ms  
// 第3次重试：4000ms
```

#### 重试条件
```java
// 控制命令：最多重试2次，更严格
if (timeoutType == TimeoutTypeEnum.CONTROL_TIMEOUT) {
    return retryCount < Math.min(maxRetryCount, 2);
}

// 读取命令：最多重试3次，相对宽松
return retryCount < maxRetryCount;
```

## 🔧 使用方式

### 1. 控制命令超时处理
```java
// 在 StrategyService.handlerResult() 中
if(msgResult.getInteger("code") == ErrorCode.REQUEST_TIMEOUT.getCode()){
    // 使用超时处理服务处理控制命令超时
    JSONArray timeoutResult = timeoutHandlerService.handleControlTimeout(commandId, gatewaycode, 0);
    if (!timeoutResult.isEmpty()) {
        JSONObject timeoutObj = timeoutResult.getJSONObject(0);
        cmdstate = timeoutObj.getString("cmdstate");
    }
}
```

### 2. 数据读取超时处理
```java
// 在 StrategyService.handlerReadResult() 中
if(msgResult.getLong("code")==ErrorCode.REQUEST_TIMEOUT.getCode()){
    // 使用超时处理服务处理读取超时
    return timeoutHandlerService.handleReadTimeout(gatewaycode, null, 0);
}
```

### 3. 重试机制使用
```java
// 异步重试控制命令
CompletableFuture<Result> retryResult = retryService.retryControlCommand(
    commandId, gatewayCode, mqttCommand, retryCount);

// 异步重试读取命令
CompletableFuture<List<DataCodeValDTO>> retryResult = retryService.retryReadCommand(
    gatewayCode, mqttCommand, retryCount);
```

## 📊 改进效果

### 1. 可靠性提升
- **网络抖动容忍**: 通过重试机制处理临时网络问题
- **状态明确**: 控制命令超时后明确设备状态
- **资源清理**: 完整的映射关系清理机制

### 2. 性能优化
- **异步重试**: 不阻塞主流程
- **指数退避**: 避免网络拥塞
- **差异化策略**: 根据操作类型优化处理

### 3. 监控增强
- **详细日志**: 记录超时类型、重试次数等信息
- **状态跟踪**: 数据库中记录详细的超时状态
- **指标统计**: 便于监控和分析

## 🚀 建议的实施步骤

1. **第一阶段**: 实现基础的差异化超时处理
2. **第二阶段**: 添加重试机制和指数退避
3. **第三阶段**: 完善监控和日志记录
4. **第四阶段**: 性能调优和压力测试

## ⚠️ 注意事项

1. **重试次数控制**: 避免无限重试导致系统负载过高
2. **并发控制**: 重试时要考虑并发请求的影响
3. **状态一致性**: 确保重试过程中状态的一致性
4. **监控告警**: 设置合适的超时和重试告警阈值
