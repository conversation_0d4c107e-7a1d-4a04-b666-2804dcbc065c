package com.siact.control.service;

import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.enume.TimeoutTypeEnum;
import com.siact.control.mqtt.SynMqttSender;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 重试服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class RetryService {

    @Autowired
    private SynMqttSender synMqttSender;
    
    @Autowired
    private TimeoutHandlerService timeoutHandlerService;
    
    @Autowired
    private StrategyService strategyService;

    @Value("${mqtt.maxRetryCount:3}")
    private int maxRetryCount;

    @Value("${mqtt.retryBaseDelay:1000}")
    private long retryBaseDelay;

    /**
     * 异步重试控制命令
     * @param commandId 命令ID
     * @param gatewayCode 网关编码
     * @param mqttCommand MQTT命令
     * @param retryCount 当前重试次数
     * @return 重试结果
     */
    @Async
    public CompletableFuture<Result> retryControlCommand(Long commandId, String gatewayCode, 
                                                        JSONObject mqttCommand, int retryCount) {
        if (!timeoutHandlerService.shouldRetry(retryCount, TimeoutTypeEnum.CONTROL_TIMEOUT)) {
            log.warn("控制命令重试次数已达上限 - 命令ID: {}, 网关: {}, 重试次数: {}", 
                    commandId, gatewayCode, retryCount);
            return CompletableFuture.completedFuture(Result.error("重试次数已达上限"));
        }

        try {
            // 计算重试延迟
            long delay = timeoutHandlerService.getRetryDelay(retryCount, TimeoutTypeEnum.CONTROL_TIMEOUT);
            log.info("控制命令准备重试 - 命令ID: {}, 网关: {}, 重试次数: {}, 延迟: {}ms", 
                    commandId, gatewayCode, retryCount + 1, delay);
            
            // 等待延迟时间
            TimeUnit.MILLISECONDS.sleep(delay);
            
            // 重新发送命令
            String topic = strategyService.getIssueTopic(gatewayCode);
            DefaultFuture future = synMqttSender.sendMessage(gatewayCode, topic, mqttCommand);
            future.setRetryCount(retryCount + 1);
            
            // 等待响应
            return CompletableFuture.completedFuture(Result.ok("重试成功"));
            
        } catch (Exception e) {
            log.error("控制命令重试失败 - 命令ID: {}, 网关: {}, 重试次数: {}", 
                    commandId, gatewayCode, retryCount + 1, e);
            return CompletableFuture.completedFuture(Result.error("重试失败: " + e.getMessage()));
        }
    }

    /**
     * 异步重试读取命令
     * @param gatewayCode 网关编码
     * @param mqttCommand MQTT命令
     * @param retryCount 当前重试次数
     * @return 重试结果
     */
    @Async
    public CompletableFuture<List<DataCodeValDTO>> retryReadCommand(String gatewayCode, 
                                                                   JSONObject mqttCommand, int retryCount) {
        if (!timeoutHandlerService.shouldRetry(retryCount, TimeoutTypeEnum.READ_TIMEOUT)) {
            log.warn("读取命令重试次数已达上限 - 网关: {}, 重试次数: {}", gatewayCode, retryCount);
            return CompletableFuture.completedFuture(
                    timeoutHandlerService.handleReadTimeout(gatewayCode, null, retryCount));
        }

        try {
            // 计算重试延迟
            long delay = timeoutHandlerService.getRetryDelay(retryCount, TimeoutTypeEnum.READ_TIMEOUT);
            log.info("读取命令准备重试 - 网关: {}, 重试次数: {}, 延迟: {}ms", 
                    gatewayCode, retryCount + 1, delay);
            
            // 等待延迟时间
            TimeUnit.MILLISECONDS.sleep(delay);
            
            // 重新发送命令
            String topic = strategyService.getIssueTopic(gatewayCode);
            DefaultFuture future = synMqttSender.sendReadMessage(gatewayCode, topic, mqttCommand);
            future.setRetryCount(retryCount + 1);
            
            // 这里需要等待响应并处理结果
            // 实际实现中需要与现有的响应处理逻辑集成
            
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            log.error("读取命令重试失败 - 网关: {}, 重试次数: {}", gatewayCode, retryCount + 1, e);
            return CompletableFuture.completedFuture(
                    timeoutHandlerService.handleReadTimeout(gatewayCode, null, retryCount));
        }
    }

    /**
     * 批量重试控制命令
     * @param commandMap 命令映射
     * @param retryCount 重试次数
     * @return 重试结果
     */
    @Async
    public CompletableFuture<Result> batchRetryControlCommands(Map<String, JSONObject> commandMap, 
                                                              int retryCount) {
        log.info("开始批量重试控制命令，网关数量: {}, 重试次数: {}", commandMap.size(), retryCount + 1);
        
        try {
            // 计算重试延迟
            long delay = timeoutHandlerService.getRetryDelay(retryCount, TimeoutTypeEnum.CONTROL_TIMEOUT);
            TimeUnit.MILLISECONDS.sleep(delay);
            
            // 重新发送所有命令
            for (Map.Entry<String, JSONObject> entry : commandMap.entrySet()) {
                String gatewayCode = entry.getKey();
                JSONObject command = entry.getValue();
                
                try {
                    String topic = strategyService.getIssueTopic(gatewayCode);
                    DefaultFuture future = synMqttSender.sendMessage(gatewayCode, topic, command);
                    future.setRetryCount(retryCount + 1);
                } catch (Exception e) {
                    log.error("网关 {} 重试失败", gatewayCode, e);
                }
            }
            
            return CompletableFuture.completedFuture(Result.ok("批量重试完成"));
            
        } catch (Exception e) {
            log.error("批量重试控制命令失败", e);
            return CompletableFuture.completedFuture(Result.error("批量重试失败: " + e.getMessage()));
        }
    }

    /**
     * 检查是否应该进行重试
     * @param retryCount 当前重试次数
     * @param timeoutType 超时类型
     * @return 是否应该重试
     */
    public boolean shouldRetry(int retryCount, TimeoutTypeEnum timeoutType) {
        return timeoutHandlerService.shouldRetry(retryCount, timeoutType);
    }

    /**
     * 获取下次重试的延迟时间
     * @param retryCount 重试次数
     * @param timeoutType 超时类型
     * @return 延迟时间（毫秒）
     */
    public long getNextRetryDelay(int retryCount, TimeoutTypeEnum timeoutType) {
        return timeoutHandlerService.getRetryDelay(retryCount, timeoutType);
    }
}
