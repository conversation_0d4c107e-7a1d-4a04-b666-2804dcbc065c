package com.siact.control.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.control.entity.CommandDetailEntity;
import com.siact.control.utils.PageUtils;

import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 17:14:20
 */
public interface CommandDetailService extends IService<CommandDetailEntity> {

    PageUtils queryPage(Map<String, Object> params);
    CommandDetailEntity getCommandByCommandId(Long commandId);

    boolean updateByCommandId(Long commandId, String commVerifySuccess,Integer endFlag);
    Long insert(CommandDetailEntity commandDetailEntity);


}

