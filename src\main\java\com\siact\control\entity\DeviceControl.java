package com.siact.control.entity;/**
 * @Package com.siact.wjycontrol.entity
 * @description:
 * <AUTHOR>
 * @create 2024/6/3 17:26
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName DeviceControl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 17:26
 * @Version 1.0
 **/
@Data
@ApiModel(value = "设备控制实体类",description = "设备控制实体类")
public class DeviceControl {

    @ApiModelProperty(value = "设备属性编码",position = 1)
    private String propCode;
    @ApiModelProperty(value = "目标值",position = 2)
    private String targetValue;

}
