package com.siact.control.mqtt;


import com.alibaba.fastjson.JSONObject;
import com.siact.control.utils.DefaultFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqttResHandler {

    /**
     * 处理MQTT响应消息
     * @param msg 响应消息
     */
    public void deal(Message msg) {
        //根据messageId判断是否是本机发送的消息
        Long msgId = msg.getMessageId();
        String gatewayCode = msg.getGatewayCode();
        log.debug("收到MQTT响应消息，ID: {}, 网关: {}, 内容: {}", msgId, gatewayCode, msg.getPlayLoad());
        if (DefaultFuture.contains(gatewayCode, msgId)) {
            log.debug("找到与网关 {} 和消息ID {} 匹配的请求，处理响应", gatewayCode, msgId);
            DefaultFuture.received(msg);
        }
    }

}

