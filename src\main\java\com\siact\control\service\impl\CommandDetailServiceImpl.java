package com.siact.control.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.control.dao.CommandDetailDao;
import com.siact.control.entity.CommandDetailEntity;
import com.siact.control.service.CommandDetailService;
import com.siact.control.utils.PageUtils;
import com.siact.control.utils.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("commandDetailService")
public class CommandDetailServiceImpl extends ServiceImpl<CommandDetailDao, CommandDetailEntity> implements CommandDetailService {

    @Autowired
    CommandDetailDao commandDetailDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<CommandDetailEntity> page = this.page(
                new Query<CommandDetailEntity>().getPage(params),
                new QueryWrapper<CommandDetailEntity>()
        );

        return new PageUtils(page);
    }

    @Override
    public CommandDetailEntity getCommandByCommandId(Long commandId) {
        QueryWrapper<CommandDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("id", commandId);
        CommandDetailEntity commandDetailEntity = baseMapper.selectOne(wrapper);
        return commandDetailEntity;
    }

    @Override
    public boolean updateByCommandId(Long commandId, String commVerifySuccess, Integer endFlag) {
        CommandDetailEntity commandDetailEntity = new CommandDetailEntity();
        commandDetailEntity.setId(commandId);
        commandDetailEntity.setCommandStatus(commVerifySuccess);
        commandDetailEntity.setEndFlag(endFlag);
        int i = commandDetailDao.updateById(commandDetailEntity);
        return i > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public Long insert(CommandDetailEntity commandDetailEntity) {
        commandDetailDao.insert(commandDetailEntity);
        return commandDetailEntity.getId();
    }


}