spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *********************************************************************************************************************
      username: siact_xm
      password: Siact_all_xm930_sql
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

isTest: false
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto

mqtt:
  username: aymdtmqqt
  password: aymdtmqqt
  url: tcp://193.2.9.152:1883
  in-client-id: ${random.value}
  out-client-id: ${random.value}
  clientId: ${random.int}
  defaultTopic: /ARM/+/+/result
  timeout: 100
  keepalive: 100
  willData: 100
  completionTimeout: 20
  clearSession: true
  prefixTopic: /ARM/
  suffixTopic: /V2.1.3/cmd
server:
  port: 9200
