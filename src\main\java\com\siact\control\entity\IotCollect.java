package com.siact.control.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class IotCollect  implements Serializable {

    private Long creartorId;
    private String creatime;

    private Long tenantid;

    private String accuracy;

    private String devcode;

    private String devproperty;

    private String gatedevcode;

    private String gatewaycode;

    private String itemid;

    private String magnification;

    private String productcode;

    private Long gatewaytype;

    private String projectid;

    private Long deviceid;

    private String devicename;

    private String devpropertyid;

    private String devpropertyname;

    private String devpropertyunit;


    private String dataType;

    private Long collectType;

    private Integer category;

    private Integer priority;

    private Integer iotType;

}