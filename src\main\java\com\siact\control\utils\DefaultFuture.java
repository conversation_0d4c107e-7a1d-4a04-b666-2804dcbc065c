package com.siact.control.utils;


import com.siact.control.mqtt.Message;
import com.siact.control.enume.TimeoutTypeEnum;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Data
@Slf4j
public class DefaultFuture implements Future {
    // 使用复合键 "commandId:gatewayCode" 作为唯一键
    private static final Map<String, DefaultFuture> FUTURES = new ConcurrentHashMap<>();
    private final Lock lock = new ReentrantLock();
    private final Condition done = lock.newCondition();
    private int timeout;
    private long id;
    private String gatewayCode; // 网关编号字段
    private final long start = System.currentTimeMillis();
    private boolean cancel = false;
    public final static AtomicLong REQUEST_ID_GEN = new AtomicLong(0);
    private Message msg;
    private TimeoutTypeEnum timeoutType; // 超时类型

    /**
     * 获取复合键
     *
     * @param gatewayCode 网关编号
     * @param commandId   命令ID
     * @return 复合键
     */
    private static String getCompositeKey(String gatewayCode, Long commandId) {
        return commandId + ":" + gatewayCode;
    }

    /**
     * 新构造函数，带网关编号
     *
     * @param id          命令ID
     * @param gatewayCode 网关编号
     * @param timeout     超时时间(秒)
     */
    public DefaultFuture(Long id, String gatewayCode, int timeout) {
        this(id, gatewayCode, timeout, TimeoutTypeEnum.CONTROL_TIMEOUT);
    }

    /**
     * 完整构造函数
     *
     * @param id          命令ID
     * @param gatewayCode 网关编号
     * @param timeout     超时时间(秒)
     * @param timeoutType 超时类型
     */
    public DefaultFuture(Long id, String gatewayCode, int timeout, TimeoutTypeEnum timeoutType) {
        this.id = id;
        this.timeout = timeout;
        this.gatewayCode = gatewayCode;
        this.timeoutType = timeoutType;
        String key = getCompositeKey(gatewayCode, id);
        FUTURES.put(key, this);
    }


    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        if (gatewayCode != null) {
            FUTURES.remove(getCompositeKey(gatewayCode, id));
        }
        this.cancel = true;
        return true;
    }

    @Override
    public boolean isCancelled() {
        return cancel;
    }

    @Override
    public boolean isDone() {
        return msg != null;
    }

    @SneakyThrows
    @Override
    public Message get() {
        return get(timeout, TimeUnit.SECONDS);
    }

    @Override
    public Message get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        if (timeout <= 0) {
            throw new RuntimeException("参数错误");
        }
        if (!isDone()) {
            long start = System.currentTimeMillis();
            lock.lock();
            try {
                while (!isDone()) {
                    done.await(timeout, TimeUnit.SECONDS);
                    if (isDone() || System.currentTimeMillis() - start > timeout) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                lock.unlock();
            }
            if (!isDone()) {
                Message timeoutResponse = createTimeoutResponse();
                this.doReceived(timeoutResponse);  // 设置响应并通知等待线程
                return this.msg;
            }
        }
        return msg;
    }

    private long getStartTimestamp() {
        return start;
    }

    /**
     * 检查是否包含指定网关和ID的请求
     *
     * @param gatewayCode 网关编号
     * @param msgId       消息ID
     * @return 是否包含
     */
    public static boolean contains(String gatewayCode, Long msgId) {
        return FUTURES.containsKey(getCompositeKey(gatewayCode, msgId));
    }

    /**
     * 处理收到的消息（向后兼容）
     * 首先尝试使用消息中的网关编号，如果没有则尝试查找所有匹配ID的请求
     *
     * @param msg 响应消息
     */
    public static void received(Message msg) {
        String key = getCompositeKey(msg.getGatewayCode(), msg.getMessageId());
        DefaultFuture future = FUTURES.remove(key);
        if (future != null) {
            future.doReceived(msg);
            return;
        }


    }

    private void doReceived(Message res) {
        lock.lock();
        try {
            this.msg = res;
            if (done != null) {
                done.signal();
            }
        } finally {
            lock.unlock();
        }
    }

    /**
     * 创建超时响应消息
     */
    private Message createTimeoutResponse() {
        Message timeoutResponse = new Message();
        timeoutResponse.setMessageId(this.id);
        timeoutResponse.setGatewayCode(this.gatewayCode);

        // 根据超时类型创建不同的响应（控制领域不重试）
        String responsePayload;
        if (timeoutType == TimeoutTypeEnum.READ_TIMEOUT) {
            responsePayload = String.format(
                "{\"code\":504,\"msg\":\"数据读取超时\",\"timeoutType\":\"%s\",\"timestamp\":%d,\"data\":null}",
                timeoutType.getMessage(), System.currentTimeMillis());
        } else {
            responsePayload = String.format(
                "{\"code\":504,\"msg\":\"控制命令超时\",\"timeoutType\":\"%s\",\"timestamp\":%d,\"data\":null}",
                timeoutType.getMessage(), System.currentTimeMillis());
        }

        timeoutResponse.setPlayLoad(responsePayload);
        return timeoutResponse;
    }

    private static class RemotingInvocationTimeoutScan implements Runnable {

        @Override
        public void run() {
            while (true) {
                try {
                    for (Map.Entry<String, DefaultFuture> entry : FUTURES.entrySet()) {
                        DefaultFuture future = entry.getValue();
                        if (future == null || future.isDone()) {
                            continue;
                        }
                        long elapsed = System.currentTimeMillis() - future.getStartTimestamp();
                        if (elapsed > future.getTimeout() * 1000) {
                            String gatewayInfo = future.getGatewayCode() != null ? "，网关编号: " + future.getGatewayCode() : "";
                            log.debug("请求ID {} 超时检测到超时，已经过 {} 毫秒，超时设置为 {} 秒{}，超时类型: {}",
                                    future.getId(), elapsed, future.getTimeout(), gatewayInfo,
                                    future.getTimeoutType() != null ? future.getTimeoutType().getMessage() : "未知");

                            Message timeoutResponse = future.createTimeoutResponse();
                            DefaultFuture.received(timeoutResponse);
                        }
                    }
                    Thread.sleep(30);
                } catch (Throwable e) {
                    log.error("扫描MQTT响应超时时发生异常", e);
                }
            }
        }
    }

    static {
        Thread th = new Thread(new RemotingInvocationTimeoutScan(), "MqttResponseTimeoutScanTimer");
        th.setDaemon(true);
        th.start();
    }
}

