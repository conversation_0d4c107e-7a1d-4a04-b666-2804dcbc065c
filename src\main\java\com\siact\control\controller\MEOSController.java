package com.siact.control.controller;

import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.entity.IssueCommandVo;
import com.siact.control.service.CommandDetailService;
import com.siact.control.service.CommonService;
import com.siact.control.service.StrategyService;
import com.siact.control.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Package com.siact.wjycontrol.controller
 * @description: 热泵机组出水温度控制
 * <AUTHOR>
 * @create 2024/6/3 17:23
 */


@Slf4j
@RestController
@RequestMapping("control/")
@Api(tags = "控制接口")
public class MEOSController {
    @Autowired
    StrategyService strategyService;
    @Autowired
    CommonService commonService;
    @Autowired
    CommandDetailService commandDetailService;
    /**
     * @return
     * <AUTHOR>
     * 控制命令下发
     * @Param JSONObject
     **/

    @ApiOperation("下发控制命令")
    @PostMapping("/issue")
    public Result commandIssue(@RequestBody IssueCommandVo issueCommandVo) {
        log.info("接收控制命令 - 用户: {}, 设备数量: {}, 命令详情: {}",
                issueCommandVo.getUserAccount(),
                issueCommandVo.getIssueDetail() != null ? issueCommandVo.getIssueDetail().size() : 0,
                issueCommandVo.getIssueDetail());

        //存储命令详情
        Long commandId = commonService.saveCommand(issueCommandVo);
        //根据模型对照点位，并下发命令
        Result exeR = commonService.execute(issueCommandVo, commandId);
        //endFlag表示该命令执行结束
        boolean flagUpdate = commandDetailService.updateByCommandId(commandId, String.valueOf(exeR.getData()), 0);

        if (exeR.getCode() == 0) {
            log.info("控制命令执行成功 - 命令ID: {}", commandId);
        } else {
            log.error("控制命令执行失败 - 命令ID: {}, 错误: {}", commandId, exeR.getMsg());
        }

        return exeR;
    }


    @ApiOperation("数据实时值查询")
    @PostMapping("/queryFeedbackVal")
    public Result<List<DataCodeValDTO>> queryFeedbackVal(@RequestBody List<String> dataCodeList) {
        log.info("接收数据查询请求 - 数据点数量: {}, 查询内容: {}",
                dataCodeList != null ? dataCodeList.size() : 0, dataCodeList);

        List<DataCodeValDTO> result = commonService.getRealTimeData(dataCodeList);

        if (result != null && !result.isEmpty()) {
            log.info("数据查询成功 - 返回数据条数: {}, 查询结果: {}", result.size(), result);
        } else {
            log.error("数据查询失败 - 未获取到任何数据");
        }

        return Result.ok(result);
    }


}
