package com.siact.control.enume;

/**
 * 控制事务状态枚举
 * <AUTHOR>
 */
public enum ControlTransactionStateEnum {
    PREPARING(1, "准备阶段"),
    EXECUTING(2, "执行阶段"),
    COMMITTING(3, "提交阶段"),
    COMMITTED(4, "已提交"),
    ROLLING_BACK(5, "回滚阶段"),
    ROLLED_BACK(6, "已回滚"),
    FAILED(7, "失败"),
    TIMEOUT(8, "超时");

    private final Integer code;
    private final String message;

    ControlTransactionStateEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
