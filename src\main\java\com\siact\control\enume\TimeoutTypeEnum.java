package com.siact.control.enume;

/**
 * 超时类型枚举
 * <AUTHOR>
 */
public enum TimeoutTypeEnum {
    CONTROL_TIMEOUT(1, "控制命令超时"),
    READ_TIMEOUT(2, "数据读取超时"),
    NETWORK_TIMEOUT(3, "网络连接超时");

    private final Integer code;
    private final String message;

    TimeoutTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
