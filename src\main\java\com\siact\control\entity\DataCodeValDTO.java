package com.siact.control.entity;/**
 * @Package com.siact.control.entity
 * @description:
 * <AUTHOR>
 * @create 2024/11/12 13:51
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DataCodeValDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/12 13:51
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "属性值实体类",description = "属性值实体类")
public class DataCodeValDTO {
    @ApiModelProperty(value = "设备属性编码",position = 1)
    private String dataCode;
    @ApiModelProperty(value = "值",position = 2)
    private String val;
}
