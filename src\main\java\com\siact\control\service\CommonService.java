package com.siact.control.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.api.R;
import com.siact.control.entity.CommandDetailEntity;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.entity.DeviceControl;
import com.siact.control.entity.IssueCommandVo;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.siact.control.service
 * @description:
 * @create 2023/5/8 17:59
 */
@Component
@Slf4j
public class CommonService {

    @Autowired
    CommandDetailService commandDetailService;
    @Autowired
    DeviceModelService deviceModelService;
    @Autowired
    StrategyService strategyService;

    public Long saveCommand(IssueCommandVo issueCommandVo) {

        //1.将解密后的命令保存
        CommandDetailEntity commandDetailEntity = new CommandDetailEntity();
        commandDetailEntity.setIssueDetail(JSON.toJSONString(issueCommandVo.getIssueDetail()));
        commandDetailEntity.setCreateBy(issueCommandVo.getUserAccount());
        commandDetailEntity.setEndFlag(1);
        Long commandId = commandDetailService.insert(commandDetailEntity);
        log.info("控制命令入库成功======"+commandId);
        return commandId;
    }


    /**
     * @return
     * <AUTHOR>
     * @Description // 任务执行
     * @Date 13:52 2023/8/17
     * @Param
     **/
    public Result execute(IssueCommandVo issueCommandVo, Long commandId) {
        List<DeviceControl> issueDetail = issueCommandVo.getIssueDetail();
        //读取mysql模型数据到内存中
        deviceModelService.getAllPropCodeAndIotType();
        //1.将JSONArray commandDetail转化为 ===》<devCode + "α" + propCode,value> ===》  为<gateway,<point,value>>
        Map<String, Map<String, String>> deviceControlMap = strategyService.getDeviceControlMap(issueDetail);
        //2.将命令组装称为MQTT需要的格式下发
        Result mapResult = strategyService.issueMessage(commandId, deviceControlMap);
        return mapResult.getCode() == 0 ? Result.ok(mapResult.getData()) : Result.error(mapResult.getCode(), mapResult.getMsg());
    }

    public List<DataCodeValDTO> getRealTimeData(List<String> dataCodeList) {
        //将请求的dataCodeList转化为<gatewaycode,List<itemid>>
        Map<String, List<String>> gatewayItemIdMap = strategyService.getGatewayItemIdMap(dataCodeList);
        //封装下发mqtt的指令
        return strategyService.issueRealTimeDataMessage(gatewayItemIdMap);
    }
}