package com.siact.control.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.control.entity
 * @description: 前端命令下发Vo类
 * @create 2023/4/3 13:50
 */
@ApiModel("控制指令详情")
@Data
public class IssueCommandVo{
    /**
     * 命令详情
     */
    @ApiModelProperty(value = "命令详情",position = 1)
    private List<DeviceControl> issueDetail;
    /**
     * 创建用户
     */
    @ApiModelProperty(value = "用户",position = 2)
    private String userAccount;

}
