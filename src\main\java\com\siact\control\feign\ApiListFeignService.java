package com.siact.control.feign;

import com.siact.control.entity.DeviceProp;
import com.siact.control.entity.IotCollect;
import com.siact.control.utils.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.control.feign
 * @description: APIlist feign接口
 * @create 2024/10/18 15:03
 */
@FeignClient(name = "ApiListService",url = "${siact-apiList.url}")
public interface ApiListFeignService {


    @RequestMapping(value = "/api/dt2/ins/v1/v1/prop/list/kz",method = RequestMethod.GET)
    Result<List<DeviceProp>> getPropListByType();

    @RequestMapping(value = "/api/iot/v1/v1/getItemidByPropcode",method = RequestMethod.POST)
    Result<List<IotCollect>> getItemidDataByPropCode(@RequestBody List<String> propCodeList);
}
