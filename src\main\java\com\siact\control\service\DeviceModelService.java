package com.siact.control.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.control.entity.DeviceModelEntity;
import com.siact.control.utils.PageUtils;


import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-09 16:16:57
 */
public interface DeviceModelService extends IService<DeviceModelEntity> {

    PageUtils queryPage(Map<String, Object> params);

    DeviceModelEntity getDeviceModelByPropCode(String propCode);

    void getAllPropCodeAndIotType();

    void getAllModel();
}

