package com.siact.control.task;

/**
 * @Package com.siact.control.task
 * @description: 定时任务
 * <AUTHOR>
 * @create 2024/10/18 11:21
 */

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.siact.control.entity.DeviceModelEntity;
import com.siact.control.entity.DeviceProp;
import com.siact.control.entity.IotCollect;
import com.siact.control.feign.ApiListFeignService;
import com.siact.control.service.DeviceModelService;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ScheduleTask {

    @Autowired
    private ApiListFeignService apiListFeignService;
    @Autowired
    private DeviceModelService deviceModelService;

    //@Scheduled(fixedRate = 3 * 60 * 1000)
    public void syncIotCollectData() {
        log.info("开始同步数据");
        //获取数字孪生中所有可控实例属性
        Result<List<DeviceProp>> propListByType = apiListFeignService.getPropListByType();
        if (ObjectUtil.isNotEmpty(propListByType) && ObjectUtil.isNotEmpty(propListByType.getData())) {
            List<String> propList = propListByType.getData().stream().map(deviceProp -> deviceProp.getDataCode()).collect(Collectors.toList());
            //请求apiList中接口获取iot_collect中的数据
            Result<List<IotCollect>> itemidDataByPropCode = apiListFeignService.getItemidDataByPropCode(propList);
            if (ObjectUtil.isNotEmpty(itemidDataByPropCode) && ObjectUtil.isNotEmpty(itemidDataByPropCode.getData())) {
                //将IotCollect转化为DeviceModelEntity,并将数据存储到mysql中
                for (IotCollect iotCollect : itemidDataByPropCode.getData()) {
                    DeviceModelEntity deviceModelEntity = new DeviceModelEntity();
                    deviceModelEntity.setDevCode(iotCollect.getDevcode());
                    deviceModelEntity.setDevProperty(iotCollect.getDevproperty());
                    deviceModelEntity.setItemid(iotCollect.getItemid());
                    deviceModelEntity.setGatewaycode(iotCollect.getGatewaycode());
                    deviceModelEntity.setIotType(iotCollect.getIotType());
                    deviceModelEntity.setDatatype(Integer.parseInt(iotCollect.getDataType()));
                    UpdateWrapper<DeviceModelEntity> wrapper = new UpdateWrapper<>();
                    wrapper.eq("devproperty", deviceModelEntity.getDevProperty());
                    try {
                        deviceModelService.saveOrUpdate(deviceModelEntity, wrapper);
                    } catch (Exception e) {
                        log.error("iot_collect数据同步失败");
                    }

                }
            }

        }

    }
}
