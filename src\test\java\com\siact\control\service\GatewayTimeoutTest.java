package com.siact.control.service;

import com.siact.control.config.GatewayRequestConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 网关超时机制测试类
 * 验证超时控制的有效性
 */
@SpringBootTest
@Slf4j
public class GatewayTimeoutTest {

    @Autowired
    private GatewayLockService gatewayLockService;
    
    @Autowired
    private GatewayRequestConfig gatewayRequestConfig;

    /**
     * 测试网关锁超时机制
     */
    @Test
    public void testGatewayLockTimeout() throws InterruptedException {
        String testGateway = "TIMEOUT_TEST_GATEWAY";
        int threadCount = 3;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    if (threadIndex == 0) {
                        // 第一个线程模拟长时间操作（超过超时时间）
                        String result = gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                            log.info("线程{}开始长时间操作", threadIndex);
                            Thread.sleep(15000); // 15秒，超过默认超时时间
                            return "Long-Operation-Result";
                        });
                        log.info("线程{}完成长时间操作: {}", threadIndex, result);
                    } else {
                        // 其他线程模拟正常操作，但会因为超时而失败
                        try {
                            String result = gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                                log.info("线程{}执行正常操作", threadIndex);
                                Thread.sleep(1000);
                                return "Normal-Operation-Result-" + threadIndex;
                            });
                            log.info("线程{}完成正常操作: {}", threadIndex, result);
                        } catch (RuntimeException e) {
                            log.warn("线程{}操作超时: {}", threadIndex, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("线程{}执行失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("网关锁超时测试完成，总耗时: {}ms", (endTime - startTime));
    }

    /**
     * 测试配置的有效性
     */
    @Test
    public void testGatewayRequestConfig() {
        log.info("网关请求配置测试:");
        log.info("- 请求超时时间: {}秒", gatewayRequestConfig.getEffectiveTimeoutSeconds());
        log.info("- 锁超时时间: {}秒", gatewayRequestConfig.getEffectiveLockTimeoutSeconds());
        log.info("- 队列最大大小: {}", gatewayRequestConfig.getEffectiveMaxQueueSize());
        log.info("- 是否启用超时: {}", gatewayRequestConfig.getEnableTimeout());
    }

    /**
     * 测试并发超时场景
     */
    @Test
    public void testConcurrentTimeout() throws InterruptedException {
        String testGateway = "CONCURRENT_TIMEOUT_GATEWAY";
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 设置较短的超时时间进行测试
                    String result = gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                        log.info("线程{}开始执行，模拟MQTT请求", threadIndex);
                        // 模拟MQTT请求时间
                        Thread.sleep(2000);
                        return "Result-" + threadIndex;
                    }, 3); // 3秒超时
                    
                    log.info("线程{}执行成功: {}", threadIndex, result);
                    
                } catch (Exception e) {
                    if (e.getMessage().contains("超时")) {
                        log.warn("线程{}执行超时: {}", threadIndex, e.getMessage());
                    } else {
                        log.error("线程{}执行失败: {}", threadIndex, e.getMessage(), e);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("并发超时测试完成，总耗时: {}ms", (endTime - startTime));
        
        // 验证：由于超时控制，总耗时应该远小于 threadCount * 2000ms
        long maxExpectedTime = threadCount * 2000;
        long actualTime = endTime - startTime;
        log.info("预期最大耗时: {}ms, 实际耗时: {}ms", maxExpectedTime, actualTime);
        
        if (actualTime < maxExpectedTime) {
            log.info("✅ 超时控制生效，避免了长时间等待");
        } else {
            log.warn("❌ 超时控制可能未生效");
        }
    }

    /**
     * 测试超时后的恢复能力
     */
    @Test
    public void testTimeoutRecovery() throws InterruptedException {
        String testGateway = "RECOVERY_TEST_GATEWAY";
        
        log.info("第一阶段：模拟超时场景");
        try {
            gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                log.info("执行会超时的操作");
                Thread.sleep(15000); // 超过超时时间
                return "Should-Timeout";
            }, 5); // 5秒超时
        } catch (Exception e) {
            log.info("预期的超时异常: {}", e.getMessage());
        }
        
        // 等待一段时间
        Thread.sleep(1000);
        
        log.info("第二阶段：验证网关锁是否正常释放");
        try {
            String result = gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                log.info("执行正常操作");
                Thread.sleep(1000);
                return "Recovery-Success";
            }, 5);
            
            log.info("✅ 网关锁恢复正常: {}", result);
        } catch (Exception e) {
            log.error("❌ 网关锁未能正常恢复: {}", e.getMessage(), e);
        }
    }
}
