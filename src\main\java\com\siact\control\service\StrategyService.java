package com.siact.control.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.entity.DeviceControl;
import com.siact.control.entity.DeviceModelEntity;
import com.siact.control.enume.ControlPathEnum;
import com.siact.control.exception.ErrorCode;
import com.siact.control.exception.ServerException;
import com.siact.control.mqtt.Message;
import com.siact.control.mqtt.SynMqttSender;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import com.siact.control.utils.TaskUtil;
import javafx.concurrent.Task;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.siact.control.service
 * @description:
 * @create 2023/4/4 11:11
 */
@Slf4j
@Service
public class StrategyService {
    @Autowired
    CommandDetailService commandDetailService;
    @Autowired
    DeviceModelService deviceModelService;
    @Autowired
    SynMqttSender synMqttSender;
    /**
     * 发布topic前缀
     */
    @Value("${mqtt.prefixTopic}")
    private String prefixTopic;

    /**
     * 发布topic后缀
     */
    @Value("${mqtt.suffixTopic}")
    private String suffixTopic;

    @Value("${isTest}")
    private boolean isTest;

    @Value("${mqtt.parallel-gateway:false}")
    private boolean parallelGateway;

    public Result issueMqtt(Map<String, JSONObject> commandMap) {

        Map<String, DefaultFuture> map = new HashMap<>();


        for (Map.Entry<String, JSONObject> entry : commandMap.entrySet()) {
            String gatewaycode = entry.getKey();
            String issueTopic = getIssueTopic(gatewaycode);
            try {
                DefaultFuture defaultFuture = synMqttSender.sendMessage(gatewaycode,issueTopic, entry.getValue());
                map.put(gatewaycode, defaultFuture);
            } catch (Exception e) {
                return Result.error(ErrorCode.REQUEST_TIMEOUT);
            }
        }
        return Result.ok(processResponse(map));
    }


    private List<DataCodeValDTO> issueReadMqtt(HashMap<String, JSONObject> gatewayCommandMap) {
        Map<String, DefaultFuture> map = new HashMap<>();

        for (Map.Entry<String, JSONObject> entry : gatewayCommandMap.entrySet()) {
            String gatewaycode = entry.getKey();
            String issueTopic = getIssueTopic(gatewaycode);
            try {
                DefaultFuture defaultFuture = synMqttSender.sendReadMessage(gatewaycode,issueTopic, entry.getValue());
                map.put(gatewaycode, defaultFuture);
            } catch (Exception e) {
                throw new ServerException(ErrorCode.REQUEST_TIMEOUT);
            }
        }
        return processReadResponse(map);
    }

    private List<DataCodeValDTO> processReadResponse(Map<String, DefaultFuture> map) {
        if (map.size() == 0) {
            return null;
        }
        
        List<DataCodeValDTO> dataCodeValDTOList = new ArrayList<>();
        // 并行处理多网关响应
        List<CompletableFuture<List<DataCodeValDTO>>> futures = new ArrayList<>();
        final Object lock = new Object(); // 用于同步访问resultArray

        // 记录开始处理的网关数量
        log.info("开始并行处理 {} 个网关的响应", map.size());
        Set<String> gatewaySet = new HashSet<>(map.keySet());

        for (Map.Entry<String, DefaultFuture> entry : map.entrySet()) {
            String gateway = entry.getKey();
            DefaultFuture defaultFuture = entry.getValue();

            CompletableFuture<List<DataCodeValDTO>> future = CompletableFuture.supplyAsync(() -> {
                List<DataCodeValDTO> gatewayData = new ArrayList<>();
                JSONObject resultObj;
                try {
                    log.info("开始处理网关 {} 的响应", gateway);
                    Message message = defaultFuture.get();
                    String playLoad = message.getPlayLoad();
                    resultObj = JSONObject.parseObject(playLoad);
                } catch (Exception e) {
                    log.error("处理网关 {} 读取响应时发生错误: {}", gateway, e.getMessage());
                    resultObj=  new JSONObject();
                    resultObj.put("code",  ErrorCode.REQUEST_TIMEOUT.getCode());
                }
                return handlerReadResult(resultObj, gateway, gatewayData);
            });

            futures.add(future);
        }

        // 分别获取每个网关的处理结果，无论成功还是失败
        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<List<DataCodeValDTO>> future = futures.get(i);
            String gateway = new ArrayList<>(gatewaySet).get(i);

            try {
                List<DataCodeValDTO> gatewayResult = future.join();// 使用join而不是get，这样不会抛出checked异常
                if (gatewayResult != null && !gatewayResult.isEmpty()) {
                    synchronized (lock) {
                        for (int j = 0; j < gatewayResult.size(); j++) {
                            dataCodeValDTOList.add(gatewayResult.get(j));
                        }
                    }
                } else {
                    log.debug("网关 {} 返回了空结果或无结果", gateway);
                }
            } catch (Exception e) {
                log.error("获取网关 {} 响应结果时发生未捕获的错误: {}", gateway, e.getMessage(), e);
            }
        }

        log.info("并行处理完成，共处理 {} 个网关，获得 {} 条结果", map.size(), dataCodeValDTOList.size());
        return dataCodeValDTOList;
    }

    private List<DataCodeValDTO> handlerReadResult(JSONObject msgResult, String gatewaycode, List<DataCodeValDTO> dataCodeValDTOList) {

        if(msgResult.getLong("code")==ErrorCode.REQUEST_TIMEOUT.getCode()){
            return new ArrayList<DataCodeValDTO>();
        }
        try {
            // 获取data字段的字符串值
            String dataStr = msgResult.getString("data");
            // 因为data是一个字符串形式的JSON数组，需要先解析成JSONArray
            JSONArray dataArray = JSONArray.parseArray(dataStr);
            // 遍历数组，提取MESdevid和DataValue
            for (int i = 0; i < dataArray.size(); i++) {
                DataCodeValDTO dataCodeValDTO = new DataCodeValDTO();
                JSONObject item = dataArray.getJSONObject(i);
                String mesDevId = item.getString("MESdevid");
                String dataValue = item.getString("DataValue");
                if (StrUtil.isNotBlank(TaskUtil.iotReadPropMap.getOrDefault(gatewaycode + "_" + mesDevId, null))) {
                    dataCodeValDTO.setDataCode(TaskUtil.iotReadPropMap.get(gatewaycode + "_" + mesDevId));
                    dataCodeValDTO.setVal(dataValue);
                    dataCodeValDTOList.add(dataCodeValDTO);
                    TaskUtil.iotReadPropMap.remove(gatewaycode + "_" + mesDevId);
                }
            }
        } catch (Exception e) {
            log.error("解析MQTT消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("数据解析失败", e);
        }
        return dataCodeValDTOList;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description 解析其他控制的返回结果
     * @Date 11:10 2023/8/18
     * @Param [result]
     **/
    private JSONArray handlerResult(Long commandId, JSONObject msgResult, String gatewaycode, JSONArray returnArray) {
        if (!ObjectUtils.isEmpty(msgResult)) {
            try {
                String cmdstate = "";
                String rtype = msgResult.getString("rtype");
                if(msgResult.getInteger("code") == ErrorCode.REQUEST_TIMEOUT.getCode()){
                    log.error("网关 {} 请求超时, 命令ID: {}", gatewaycode, commandId);
                    // 此时将该网关下的点位返回全部置为false
                    List<String> itemidList = TaskUtil.getItemsByCommandIdAndGateway(commandId, gatewaycode);
                    //"/415D141_1:True/415D141_0:True"
                    cmdstate = itemidList.stream().map(itemid -> itemid + ":False").collect(Collectors.joining("/"));
                }else{
                    cmdstate = msgResult.getString("cmdstate");
                }
                String[] split = cmdstate.split("/");
                if (split.length > 0) {
                    for (int i = 0; i < split.length; i++) {
                        String pointStatus = split[i];
                        String[] pointValue = pointStatus.split(":");
                        if (pointValue.length > 1) {
                            String tagId = pointValue[0];
                            String exeStatus = pointValue[1];
                            //获取网关+点位对应的模型
                            String propCode = TaskUtil.iotPropMap.get(gatewaycode + "_" + tagId);
                            if (StringUtils.isNotBlank(propCode)) {
                                JSONObject returnObj = new JSONObject();
                                returnObj.put("propCode", propCode);
                                returnObj.put("result", exeStatus.toLowerCase());
                                returnArray.add(returnObj);
                                //移除map中的网关和点位
                                TaskUtil.iotPropMap.remove(gatewaycode + "_" + tagId);
                                TaskUtil.removeCommandGatewayItemsByGateway(commandId, gatewaycode);
                            }
                        }
                    }
                }

            } catch (Exception e) {
                log.error("处理网关 {} 响应时发生异常: {}", gatewaycode, e.getMessage());
            }
        }
        return returnArray;
    }

    private JSONArray processResponse(Map<String, DefaultFuture> map) {
        if (map.size() == 0) {
            return null;
        }
        
        JSONArray resultArray = new JSONArray();
        
        if (parallelGateway) {
            // 并行处理多网关响应
            List<CompletableFuture<JSONArray>> futures = new ArrayList<>();
            final Object lock = new Object(); // 用于同步访问resultArray
            
            // 记录开始处理的网关数量
            log.info("开始并行处理 {} 个网关的响应", map.size());
            Set<String> gatewaySet = new HashSet<>(map.keySet());
            
            for (Map.Entry<String, DefaultFuture> entry : map.entrySet()) {
                String gateway = entry.getKey();
                DefaultFuture defaultFuture = entry.getValue();
                
                CompletableFuture<JSONArray> future = CompletableFuture.supplyAsync(() -> {
                    JSONArray gatewayResults = new JSONArray();
                    JSONObject resultObj;
                    Long messageId =null;
                    try {
                        log.info("开始处理网关 {} 的响应", gateway);
                        Message message = defaultFuture.get();
                        messageId = message.getMessageId();
                        String playLoad = message.getPlayLoad();
                        resultObj = JSONObject.parseObject(playLoad);
                    } catch (Exception e) {
                        log.error("处理网关 {} 控制响应时发生错误: {}", gateway, e.getMessage());
                        messageId = defaultFuture.getId();
                        resultObj=  new JSONObject();
                        resultObj.put("code",  ErrorCode.REQUEST_TIMEOUT.getCode());
                    }
                    gatewayResults = handlerResult(messageId, resultObj, gateway, gatewayResults);
                    return gatewayResults;
                });
                
                futures.add(future);
            }
            
            // 分别获取每个网关的处理结果，无论成功还是失败
            for (int i = 0; i < futures.size(); i++) {
                CompletableFuture<JSONArray> future = futures.get(i);
                String gateway = new ArrayList<>(gatewaySet).get(i);
                
                try {
                    JSONArray gatewayResult = future.join(); // 使用join而不是get，这样不会抛出checked异常
                    if (gatewayResult != null && !gatewayResult.isEmpty()) {
                        synchronized (lock) {
                            for (int j = 0; j < gatewayResult.size(); j++) {
                                resultArray.add(gatewayResult.get(j));
                            }
                        }
                    } else {
                        log.debug("网关 {} 返回了空结果或无结果", gateway);
                    }
                } catch (Exception e) {
                    log.error("获取网关 {} 响应结果时发生未捕获的错误: {}", gateway, e.getMessage(), e);
                }
            }
            
            log.info("并行处理完成，共处理 {} 个网关，获得 {} 条结果", map.size(), resultArray.size());
        } else {
            // 串行处理多网关响应
            for (String gateway : map.keySet()) {
                try {
                    Message message = map.get(gateway).get();
                    Long messageId = message.getMessageId();
                    String playLoad = message.getPlayLoad();
                    JSONObject resultObj = null;
                    try {
                        resultObj = JSONObject.parseObject(playLoad);
                    } catch (Exception e) {
                        log.error("解析网关 {} 的控制响应失败: {}", gateway, e.getMessage());
                        continue;
                    }
                    // 解析result中的结果并返回
                    resultArray = handlerResult(messageId, resultObj, gateway, resultArray);
                } catch (Exception e) {
                    log.error("处理网关 {} 的控制响应超时: {}", gateway, e.getMessage());
                }
            }
            
            log.info("串行处理完成，共处理 {} 个网关，获得 {} 条结果", map.size(), resultArray.size());
        }
        return resultArray;
    }


    public Result issueMqttMONI(Map<String, JSONObject> commandMap) {

        Map<String, DefaultFuture> map = new HashMap<>();
        JSONArray resultArray = new JSONArray();

        for (Map.Entry<String, JSONObject> entry : commandMap.entrySet()) {
            String gatewaycode = entry.getKey();
            String issueTopic = getIssueTopic(gatewaycode);
            JSONObject value = entry.getValue();
            resultArray = getReturnResultMONI(gatewaycode, value, resultArray);
        }
        return Result.ok(resultArray);

    }

    public String getIssueTopic(String gatewaycode) {
        return prefixTopic + gatewaycode + suffixTopic;
    }

    private JSONArray getReturnResultMONI(String gatewaycode, JSONObject commandObj, JSONArray resultArray) {
        Long commandId = commandObj.getLong("cid");
        JSONArray tags = commandObj.getJSONArray("tag");
        // 创建一个随机数生成器
        Random random = new Random();
        for (int i = 0; i < tags.size(); i++) {
            JSONObject tagJSONObject = tags.getJSONObject(i);
            String itemid = tagJSONObject.getString("tagpos");
            //获取网关+点位对应的模型
            String propCode = TaskUtil.iotPropMap.get(gatewaycode + "_" + itemid);
            if (StringUtils.isNotBlank(propCode)) {
                JSONObject returnObj = new JSONObject();
                returnObj.put("propCode", propCode);
                returnObj.put("result", random.nextBoolean() ? "true" : "false");
                resultArray.add(returnObj);
                //移除map中的网关和点位
                TaskUtil.iotPropMap.remove(gatewaycode + "_" + itemid);
            }
        }
        return resultArray;
    }



    //将JSONArray commandList转化为 ===》<devCode + "α" + propCode,value> ===》  为<gateway,<point,value>>
    public Map<String, Map<String, String>> getDeviceControlMap(List<DeviceControl> commandList) {
        //声明map，存放每一个网关的全部命令
        Map<String, Map<String, String>> gatewayCommandMap = new HashMap<>();
        //获取命令list，将命令放到map中
        Map<String, String> issueMap = getIssueMap(commandList);
        if (issueMap.size() > 0) {
            gatewayCommandMap = getgatewayCommandMap(issueMap);
        }

        return gatewayCommandMap;
    }

    private Map<String, Map<String, String>> getgatewayCommandMap(Map<String, String> issueMap) {
        Map<String, Map<String, String>> gatewayCommandMap = new HashMap<>();
        for (Map.Entry<String, String> issueCommand : issueMap.entrySet()) {
            //获取点位
            DeviceModelEntity entity = TaskUtil.entityMap.getOrDefault(issueCommand.getKey(), null);

            if (!ObjectUtils.isEmpty(entity)) {
                String gatewaycode = entity.getGatewaycode();
                String itemid = entity.getItemid();
                TaskUtil.iotPropMap.put(gatewaycode + "_" + itemid, issueCommand.getKey());
                //获取targetValue
                String targetValue = issueMap.getOrDefault(issueCommand.getKey(), null);
                if (!ObjectUtils.isEmpty(targetValue)) {
                    // 获取或创建该网关的命令映射
                    Map<String, String> pointMap = gatewayCommandMap.computeIfAbsent(
                            gatewaycode, k -> new HashMap<>());
                    // 设置命令
                    pointMap.put(itemid, targetValue);
                }
            } else {
                throw new ServerException(ErrorCode.MODEL_NOT_MATCH);
            }

        }

        return gatewayCommandMap;
    }



    private Map<String, String> getIssueMap(List<DeviceControl> commandList) {
        HashMap<String, String> issueMap = new HashMap<>();
        for (DeviceControl deviceControl : commandList) {
            String propCode = deviceControl.getPropCode();
            String targetValue = deviceControl.getTargetValue();
            issueMap.put(propCode, targetValue);
        }
        return issueMap;
    }

    public Result<Map<String, String>> issueMessage(Long commandId, Map<String, Map<String, String>> modelCommandMap) {
        // 保存命令ID与网关点位的映射关系
        saveCommandGatewayMapping(commandId, modelCommandMap);

        //组装发给mqtt的命令
        Map<String, JSONObject> gatewayJObj = new HashMap<>();
        for (String gatewaycode : modelCommandMap.keySet()) {
            Map<String, String> devCommand = modelCommandMap.get(gatewaycode);
            //将map组装为mqtt需要的下发格式 - 对所有网关使用相同的命令ID
            JSONObject mqttCommand = getMqttCommand(commandId, devCommand);
            //将命令放入该网关下的map中
            gatewayJObj.put(gatewaycode, mqttCommand);
        }
        if (isTest) {
            return issueMqttMONI(gatewayJObj);
        } else {
            return issueMqtt(gatewayJObj);
        }
    }

    /**
     * 保存命令ID与网关点位的映射关系
     * @param commandId 命令ID
     * @param modelCommandMap 网关与点位命令的映射
     */
    private void saveCommandGatewayMapping(Long commandId, Map<String, Map<String, String>> modelCommandMap) {
        // 将 Map<String, Map<String, String>> 转换为 Map<String, List<String>>
        Map<String, List<String>> gatewayItemsMap = new HashMap<>();
        
        for (Map.Entry<String, Map<String, String>> entry : modelCommandMap.entrySet()) {
            String gateway = entry.getKey();
            Map<String, String> pointCommands = entry.getValue();
            
            // 提取点位ID
            List<String> itemIds = new ArrayList<>(pointCommands.keySet());
            gatewayItemsMap.put(gateway, itemIds);
        }
        
        // 保存到全局映射中
        TaskUtil.saveCommandGatewayItems(commandId, gatewayItemsMap);
        log.debug("已保存命令ID {}与{}个网关的点位映射关系", commandId, gatewayItemsMap.size());
    }

    public JSONObject getMqttCommand(Long commandID, Map<String, String> devCommand) {
        JSONObject mqttCommJObject = new JSONObject();
        mqttCommJObject.put("cid", commandID);
        mqttCommJObject.put("cmd", ControlPathEnum.CMD_CONTROL.code());
        mqttCommJObject.put("mode", ControlPathEnum.MQTT.code());
        JSONArray array = new JSONArray();
        //声明一个JasonArray存放点位,用于回读点位下发给MQTT
        JSONArray tags = new JSONArray();
        for (Map.Entry<String, String> entry : devCommand.entrySet()) {
            JSONObject subObj = new JSONObject();
            subObj.put("tagpos", entry.getKey());
            
            // 将dataValue先转换为double，再转换为整数类型
            String strValue = entry.getValue();
            int intValue;
            try {
                // 统一将字符串先转为double，再转为int
                double doubleValue = Double.parseDouble(strValue);
                intValue = (int) doubleValue;
            } catch (NumberFormatException e) {
                // 如果无法转换为数字，则默认为0
                log.error("无法将值 '{}' 转换为数值", strValue);
                throw new ServerException(ErrorCode.INTERNAL_SERVER_ERROR);
            }
            
            // 将整数值设置到dataValue字段
            subObj.put("dataValue", intValue);
            
            array.add(subObj);
            tags.add(entry.getKey());
        }
        mqttCommJObject.put("tag", array);
        return mqttCommJObject;
    }

    /**
     * 根据数据编码列表获取网关项目ID映射
     *
     * @param dataCodeList 数据编码列表
     * @return 网关编码到项目ID列表的映射
     * @throws ServerException 当没有匹配的设备模型时抛出
     */
    public Map<String, List<String>> getGatewayItemIdMap(List<String> dataCodeList) {

        // 参数校验
        if (CollectionUtil.isEmpty(dataCodeList)) {
            throw new ServerException(ErrorCode.MODEL_NOT_MATCH);
        }
        // 查询设备模型
        List<DeviceModelEntity> deviceModels = queryDeviceModels(dataCodeList);
        // 构建映射关系
        return buildGatewayItemIdMap(deviceModels);
    }

    /**
     * 查询设备模型
     */
    private List<DeviceModelEntity> queryDeviceModels(List<String> dataCodeList) {
        QueryWrapper<DeviceModelEntity> wrapper = new QueryWrapper<>();
        wrapper.in("dev_property", dataCodeList);

        List<DeviceModelEntity> deviceModels = deviceModelService.list(wrapper);
        if (CollectionUtil.isEmpty(deviceModels)) {
            throw new ServerException(ErrorCode.MODEL_NOT_MATCH);
        }
        return deviceModels;
    }

    /**
     * 构建网关到项目ID的映射
     */
    private Map<String, List<String>> buildGatewayItemIdMap(List<DeviceModelEntity> deviceModels) {

        for (DeviceModelEntity deviceModel : deviceModels) {
            TaskUtil.iotReadPropMap.put(deviceModel.getGatewaycode() + "_" + deviceModel.getItemid(), deviceModel.getDevProperty());
        }
        return deviceModels.stream()
                .filter(model -> StringUtils.isNotBlank(model.getGatewaycode())
                        && StringUtils.isNotBlank(model.getItemid()))
                .collect(Collectors.groupingBy(
                        DeviceModelEntity::getGatewaycode,
                        Collectors.mapping(DeviceModelEntity::getItemid, Collectors.toList())
                ));

    }

    public List<DataCodeValDTO> issueRealTimeDataMessage(Map<String, List<String>> gatewayItemIdMap) {
        HashMap<String, JSONObject> gatewayCommandMap = new HashMap<>();
        gatewayItemIdMap.forEach((gateway, itemIds) -> {
            JSONObject mqttCommJObject = new JSONObject();
            mqttCommJObject.put("cmd", ControlPathEnum.CMD_READ.code());
            JSONArray tags = new JSONArray();
            for (String itemId : itemIds) {
                tags.add(itemId);
            }
            mqttCommJObject.put("tagID", tags);
            //下发mqtt
            gatewayCommandMap.put(gateway, mqttCommJObject);
        });
        //下发
        if (isTest) {
            return issueReadMqttMONI(gatewayCommandMap);
        } else {
            return issueReadMqtt(gatewayCommandMap);
        }

    }

    private List<DataCodeValDTO> issueReadMqttMONI(HashMap<String, JSONObject> gatewayCommandMap) {
        ArrayList<DataCodeValDTO> dataCodeValDTOS = new ArrayList<>();
        Random random = new Random();
        
        // 遍历所有网关命令，为每个网关单独生成模拟数据
        gatewayCommandMap.forEach((gatewayCode, command) -> {
            // 获取该网关下的所有propCode
            TaskUtil.iotReadPropMap.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith(gatewayCode + "_"))
                    .forEach(entry -> {
                        String propCode = entry.getValue();
                        DataCodeValDTO dataCodeValDTO = new DataCodeValDTO();
                        dataCodeValDTO.setDataCode(propCode);
                        dataCodeValDTO.setVal(String.format("%.2f", 100.0 * random.nextDouble()));
                        dataCodeValDTOS.add(dataCodeValDTO);
                        // 从映射中移除已处理的项
                        TaskUtil.iotReadPropMap.remove(entry.getKey());
                    });
        });
        
        return dataCodeValDTOS;
    }

}


