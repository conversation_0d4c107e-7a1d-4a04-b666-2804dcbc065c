package com.siact.control.enume;

/**
 * <AUTHOR>
 * @Package com.siact.control.commonenum
 * @description: 控制路径枚举类
 * @create 2023/5/22 11:30
 */
public enum ControlPathEnum {
    WEBSOCKET(1,"Web Socket"),
    MQTT(2,"MQTT"),
    CMD_CONTROL(20,"命令下发"),
    CMD_READ(11,"实时值读取"),
    CMD_TCP(51,"充电桩功率TCP控制"),
    TAG_DEV(80,"DEV009"),
    TAG_GUN(81,"GUN010"),
    TAG_POWER(82,"VALUE011");
    /** 码 */
    private Integer resultCode;

    /** 描述 */
    private String resultMsg;

    ControlPathEnum(Integer resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }
    public int code() {
        return resultCode;
    }

    public String message() {
        return resultMsg;
    }
}
