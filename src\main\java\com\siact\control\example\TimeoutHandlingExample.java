package com.siact.control.example;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.service.ControlTimeoutStrategy;
import com.siact.control.service.TimeoutHandlerService;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 超时处理使用示例（简化版）
 * 展示简化的差异化超时处理策略
 * <AUTHOR>
 */
@Slf4j
@Component
public class TimeoutHandlingExample {

    @Autowired
    private ControlTimeoutStrategy controlTimeoutStrategy;
    
    @Autowired
    private TimeoutHandlerService timeoutHandlerService;

    /**
     * 控制命令超时处理示例 - 简化处理策略
     * 场景：向3个网关发送控制命令，其中1个网关超时
     * 结果：返回所有网关结果，包括超时的网关
     */
    public void demonstrateControlTimeout() {
        log.info("=== 控制命令超时处理示例（简化版） ===");

        Long commandId = 12345L;
        Map<String, DefaultFuture> futureMap = new HashMap<>();

        // 模拟3个网关的Future对象
        // futureMap.put("gateway001", mockFuture);
        // futureMap.put("gateway002", mockTimeoutFuture); // 这个会超时
        // futureMap.put("gateway003", mockFuture);

        // 处理控制命令响应
        Result<JSONArray> result = controlTimeoutStrategy.processControlResponse(futureMap, commandId);

        if (result.getCode() == 0) {
            JSONArray results = result.getData();
            log.info("✅ 控制操作处理完成，处理网关数: {}", results.size());

            // 检查是否有超时网关
            if (result.getMsg() != null && result.getMsg().contains("超时")) {
                log.warn("⚠️ 部分网关超时: {}", result.getMsg());
                // 可以根据业务需要决定后续处理
            } else {
                log.info("✅ 所有网关执行成功");
            }
        } else {
            log.error("❌ 控制操作处理异常: {}", result.getMsg());
        }
    }

    /**
     * 数据读取超时处理示例 - Partial Success 策略
     * 场景：从5个网关读取数据，其中2个网关超时
     * 结果：返回3个正常网关的数据，记录2个网关超时
     */
    public void demonstrateReadTimeout() {
        log.info("=== 数据读取超时处理示例 ===");
        
        Map<String, DefaultFuture> futureMap = new HashMap<>();
        
        // 模拟5个网关的Future对象
        // futureMap.put("gateway001", mockFuture);
        // futureMap.put("gateway002", mockFuture);
        // futureMap.put("gateway003", mockTimeoutFuture); // 超时
        // futureMap.put("gateway004", mockFuture);
        // futureMap.put("gateway005", mockTimeoutFuture); // 超时
        
        // 处理读取命令响应
        List<DataCodeValDTO> results = controlTimeoutStrategy.processReadResponse(futureMap);
        
        log.info("📊 读取操作完成");
        log.info("总网关数: {}, 获取数据条数: {}", futureMap.size(), results.size());
        
        if (results.size() > 0) {
            log.info("✅ 成功获取部分数据，系统继续正常运行");
            
            // 处理获取到的数据
            for (DataCodeValDTO data : results) {
                log.debug("数据: {} = {}", data.getDataCode(), data.getVal());
            }
        } else {
            log.warn("⚠️ 所有网关都超时，未获取到任何数据");
        }
    }

    /**
     * 单个网关控制超时处理示例
     */
    public void demonstrateSingleGatewayControlTimeout() {
        log.info("=== 单个网关控制超时处理示例 ===");
        
        Long commandId = 67890L;
        String gatewayCode = "gateway001";
        
        // 处理单个网关的控制超时
        JSONArray timeoutResult = timeoutHandlerService.handleControlTimeout(commandId, gatewayCode);
        
        if (!timeoutResult.isEmpty()) {
            JSONObject result = timeoutResult.getJSONObject(0);
            log.error("❌ 网关 {} 控制超时", gatewayCode);
            log.error("命令状态: {}", result.getString("cmdstate"));
            log.error("失败原因: {}", result.getString("msg"));
            
            // 在实际应用中，这里会触发整个控制操作的失败
            log.error("🚨 触发整体控制失败，系统进入安全状态");
        }
    }

    /**
     * 单个网关读取超时处理示例
     */
    public void demonstrateSingleGatewayReadTimeout() {
        log.info("=== 单个网关读取超时处理示例 ===");
        
        String gatewayCode = "gateway002";
        
        // 处理单个网关的读取超时
        List<DataCodeValDTO> timeoutResult = timeoutHandlerService.handleReadTimeout(gatewayCode, null);
        
        log.warn("⚠️ 网关 {} 读取超时", gatewayCode);
        log.info("返回数据条数: {} (空数据，不影响其他网关)", timeoutResult.size());
        log.info("✅ 其他网关数据正常返回，系统继续运行");
    }

    /**
     * 超时策略判断示例（简化版）
     */
    public void demonstrateTimeoutStrategyDecision() {
        log.info("=== 超时策略判断示例（简化版） ===");

        // 模拟超时网关列表
        List<String> timeoutGateways = new ArrayList<>();
        timeoutGateways.add("gateway002");
        timeoutGateways.add("gateway004");
        int totalGateways = 5;

        // 判断是否有超时网关
        boolean hasTimeout = timeoutHandlerService.hasTimeoutGateways(timeoutGateways, totalGateways);
        log.info("是否有超时网关: {} (简化处理：记录超时但不影响整体结果)", hasTimeout);

        // 判断读取操作是否应该继续
        boolean shouldReadContinue = timeoutHandlerService.shouldReadOperationContinue(timeoutGateways, totalGateways);
        log.info("读取操作是否应该继续: {} (原因: 部分网关超时仍可返回其他数据)", shouldReadContinue);

        log.info("成功网关数: {}, 超时网关数: {}",
                totalGateways - timeoutGateways.size(), timeoutGateways.size());
    }

    /**
     * 控制超时后的处理（简化版）
     */
    @SuppressWarnings("unused")
    private void handleControlTimeout(Long commandId, String timeoutGateways) {
        log.warn("⚠️ 控制命令部分网关超时 - 命令ID: {}", commandId);
        log.warn("超时网关: {}", timeoutGateways);

        // 简化处理逻辑：
        // 1. 记录超时日志到数据库
        // 2. 可选：发送告警通知
        // 3. 继续处理其他网关的结果

        log.info("✅ 继续处理其他网关结果");
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("🚀 开始运行工业控制超时处理示例");
        
        try {
            demonstrateControlTimeout();
            Thread.sleep(1000);
            
            demonstrateReadTimeout();
            Thread.sleep(1000);
            
            demonstrateSingleGatewayControlTimeout();
            Thread.sleep(1000);
            
            demonstrateSingleGatewayReadTimeout();
            Thread.sleep(1000);
            
            demonstrateTimeoutStrategyDecision();
            
        } catch (InterruptedException e) {
            log.error("示例运行被中断", e);
        }
        
        log.info("✅ 所有示例运行完成");
    }
}
