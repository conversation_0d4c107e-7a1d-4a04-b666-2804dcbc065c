package com.siact.control.example;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.service.ControlTimeoutStrategy;
import com.siact.control.service.TimeoutHandlerService;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 超时处理使用示例（最终版）
 * 展示保持返回格式不变的超时处理策略
 * <AUTHOR>
 */
@Slf4j
@Component
public class TimeoutHandlingExample {

    @Autowired
    private ControlTimeoutStrategy controlTimeoutStrategy;
    
    @Autowired
    private TimeoutHandlerService timeoutHandlerService;

    /**
     * 控制命令超时处理示例 - 直接返回错误策略
     * 场景：向3个网关发送控制命令，其中1个网关超时
     * 结果：整个请求返回超时错误，不返回任何结果数据
     */
    public void demonstrateControlTimeout() {
        log.info("=== 控制命令超时处理示例（最终版） ===");

        Long commandId = 12345L;
        Map<String, DefaultFuture> futureMap = new HashMap<>();

        // 模拟3个网关的Future对象
        // futureMap.put("gateway001", mockFuture);
        // futureMap.put("gateway002", mockTimeoutFuture); // 这个会超时
        // futureMap.put("gateway003", mockFuture);

        // 处理控制命令响应
        Result<JSONArray> result = controlTimeoutStrategy.processControlResponse(futureMap, commandId);

        if (result.getCode() == 0) {
            JSONArray results = result.getData();
            log.info("✅ 控制操作全部成功，处理网关数: {}", results.size());
            log.info("✅ 所有网关执行成功，可以继续后续处理");
        } else {
            log.error("❌ 控制操作失败: {}", result.getMsg());
            log.error("❌ 错误码: {}", result.getCode());
            log.error("❌ 由于网关超时，整个控制操作失败，不返回任何结果");
            // 在实际应用中，这里会直接返回错误给前端
        }
    }

    /**
     * 数据读取超时处理示例 - 直接返回空列表策略
     * 场景：从5个网关读取数据，其中1个网关超时
     * 结果：整个请求返回空列表，不返回任何数据
     */
    public void demonstrateReadTimeout() {
        log.info("=== 数据读取超时处理示例（最终版） ===");

        Map<String, DefaultFuture> futureMap = new HashMap<>();

        // 模拟5个网关的Future对象
        // futureMap.put("gateway001", mockFuture);
        // futureMap.put("gateway002", mockFuture);
        // futureMap.put("gateway003", mockTimeoutFuture); // 这个会超时
        // futureMap.put("gateway004", mockFuture);
        // futureMap.put("gateway005", mockFuture);

        // 处理读取命令响应
        List<DataCodeValDTO> results = controlTimeoutStrategy.processReadResponse(futureMap);

        log.info("📊 读取操作完成");
        log.info("总网关数: {}, 获取数据条数: {}", futureMap.size(), results.size());

        if (results.size() > 0) {
            log.info("✅ 读取操作全部成功，获取到完整数据");

            // 处理获取到的数据
            for (DataCodeValDTO data : results) {
                log.debug("数据: {} = {}", data.getDataCode(), data.getVal());
            }
        } else {
            log.warn("❌ 读取操作失败，由于网关超时返回空列表");
            log.warn("❌ 前端将收到空的数据列表");
        }
    }

    /**
     * 单个网关控制超时处理示例
     */
    public void demonstrateSingleGatewayControlTimeout() {
        log.info("=== 单个网关控制超时处理示例 ===");
        
        Long commandId = 67890L;
        String gatewayCode = "gateway001";
        
        // 处理单个网关的控制超时
        JSONArray timeoutResult = timeoutHandlerService.handleControlTimeout(commandId, gatewayCode);
        
        if (!timeoutResult.isEmpty()) {
            JSONObject result = timeoutResult.getJSONObject(0);
            log.error("❌ 网关 {} 控制超时", gatewayCode);
            log.error("命令状态: {}", result.getString("cmdstate"));
            log.error("失败原因: {}", result.getString("msg"));
            
            // 在实际应用中，这里会触发整个控制操作的失败
            log.error("🚨 触发整体控制失败，系统进入安全状态");
        }
    }

    /**
     * 单个网关读取超时处理示例
     */
    public void demonstrateSingleGatewayReadTimeout() {
        log.info("=== 单个网关读取超时处理示例 ===");
        
        String gatewayCode = "gateway002";
        
        // 处理单个网关的读取超时
        List<DataCodeValDTO> timeoutResult = timeoutHandlerService.handleReadTimeout(gatewayCode, null);
        
        log.warn("⚠️ 网关 {} 读取超时", gatewayCode);
        log.info("返回数据条数: {} (空数据，不影响其他网关)", timeoutResult.size());
        log.info("✅ 其他网关数据正常返回，系统继续运行");
    }

    /**
     * 超时策略判断示例（简化版）
     */
    public void demonstrateTimeoutStrategyDecision() {
        log.info("=== 超时策略判断示例（简化版） ===");

        // 模拟超时网关列表
        List<String> timeoutGateways = new ArrayList<>();
        timeoutGateways.add("gateway002");
        timeoutGateways.add("gateway004");
        int totalGateways = 5;

        // 判断是否有超时网关
        boolean hasTimeout = timeoutHandlerService.hasTimeoutGateways(timeoutGateways, totalGateways);
        log.info("是否有超时网关: {} (简化处理：记录超时但不影响整体结果)", hasTimeout);

        // 判断读取操作是否应该继续
        boolean shouldReadContinue = timeoutHandlerService.shouldReadOperationContinue(timeoutGateways, totalGateways);
        log.info("读取操作是否应该继续: {} (原因: 部分网关超时仍可返回其他数据)", shouldReadContinue);

        log.info("成功网关数: {}, 超时网关数: {}",
                totalGateways - timeoutGateways.size(), timeoutGateways.size());
    }

    /**
     * 控制超时后的处理（简化版）
     */
    @SuppressWarnings("unused")
    private void handleControlTimeout(Long commandId, String timeoutGateways) {
        log.warn("⚠️ 控制命令部分网关超时 - 命令ID: {}", commandId);
        log.warn("超时网关: {}", timeoutGateways);

        // 简化处理逻辑：
        // 1. 记录超时日志到数据库
        // 2. 可选：发送告警通知
        // 3. 继续处理其他网关的结果

        log.info("✅ 继续处理其他网关结果");
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("🚀 开始运行工业控制超时处理示例");
        
        try {
            demonstrateControlTimeout();
            Thread.sleep(1000);
            
            demonstrateReadTimeout();
            Thread.sleep(1000);
            
            demonstrateSingleGatewayControlTimeout();
            Thread.sleep(1000);
            
            demonstrateSingleGatewayReadTimeout();
            Thread.sleep(1000);
            
            demonstrateTimeoutStrategyDecision();
            
        } catch (InterruptedException e) {
            log.error("示例运行被中断", e);
        }
        
        log.info("✅ 所有示例运行完成");
    }
}
