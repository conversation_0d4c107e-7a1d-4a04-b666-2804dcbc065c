package com.siact.control.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DataCodeValDTO;
import com.siact.control.exception.ErrorCode;
import com.siact.control.exception.ServerException;
import com.siact.control.mqtt.Message;
import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 控制超时策略服务
 * 实现控制领域和读取领域的差异化超时处理策略
 * <AUTHOR>
 */
@Slf4j
@Service
public class ControlTimeoutStrategy {

    @Autowired
    private TimeoutHandlerService timeoutHandlerService;

    /**
     * 处理控制命令的响应 - 任何一个网关超时，整体操作失败
     * @param futureMap 网关Future映射
     * @param commandId 命令ID
     * @return 处理结果
     */
    public Result<JSONArray> processControlResponse(Map<String, DefaultFuture> futureMap, Long commandId) {
        if (futureMap.isEmpty()) {
            return Result.error("没有需要处理的网关");
        }

        List<CompletableFuture<JSONArray>> futures = new ArrayList<>();
        List<String> timeoutGateways = new ArrayList<>();
        JSONArray allResults = new JSONArray();

        // 并行处理所有网关的响应
        for (Map.Entry<String, DefaultFuture> entry : futureMap.entrySet()) {
            String gateway = entry.getKey();
            DefaultFuture defaultFuture = entry.getValue();

            CompletableFuture<JSONArray> future = CompletableFuture.supplyAsync(() -> {
                JSONArray gatewayResults = new JSONArray();
                try {
                    log.info("开始处理控制命令网关 {} 的响应", gateway);
                    Message message = defaultFuture.get();
                    String playLoad = message.getPlayLoad();
                    JSONObject resultObj = JSONObject.parseObject(playLoad);
                    
                    // 检查是否超时
                    if (resultObj.getInteger("code") == ErrorCode.REQUEST_TIMEOUT.getCode()) {
                        synchronized (timeoutGateways) {
                            timeoutGateways.add(gateway);
                        }
                        // 处理控制命令超时
                        return timeoutHandlerService.handleControlTimeout(commandId, gateway);
                    } else {
                        // 正常处理响应
                        return processNormalControlResponse(commandId, resultObj, gateway);
                    }
                } catch (Exception e) {
                    log.error("处理控制命令网关 {} 响应时发生错误: {}", gateway, e.getMessage());
                    synchronized (timeoutGateways) {
                        timeoutGateways.add(gateway);
                    }
                    // 异常也当作超时处理
                    return timeoutHandlerService.handleControlTimeout(commandId, gateway);
                }
            });

            futures.add(future);
        }

        // 等待所有网关响应完成
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join(); // 等待所有完成

            // 收集所有结果
            for (CompletableFuture<JSONArray> future : futures) {
                JSONArray gatewayResult = future.get();
                allResults.addAll(gatewayResult);
            }

        } catch (Exception e) {
            log.error("等待控制命令响应时发生错误", e);
            return Result.error("控制命令处理异常");
        }

        // 检查是否有网关超时，如果有则直接返回错误
        if (timeoutHandlerService.hasTimeoutGateways(timeoutGateways, futureMap.size())) {
            log.error("控制操作失败 - 网关超时: {}", String.join(",", timeoutGateways));
            // 直接返回网关超时错误，不返回任何结果数据
            return Result.error(ErrorCode.REQUEST_TIMEOUT.getCode(),
                    String.format("网关超时: %s", String.join(",", timeoutGateways)));
        }

        log.info("控制操作全部成功完成 - 处理网关数: {}", futureMap.size());
        return Result.ok(allResults);
    }

    /**
     * 处理读取命令的响应 - 部分网关超时不影响其他网关数据返回
     * @param futureMap 网关Future映射
     * @return 处理结果
     */
    public List<DataCodeValDTO> processReadResponse(Map<String, DefaultFuture> futureMap) {
        if (futureMap.isEmpty()) {
            return new ArrayList<>();
        }

        List<CompletableFuture<List<DataCodeValDTO>>> futures = new ArrayList<>();
        List<String> timeoutGateways = new ArrayList<>();
        List<DataCodeValDTO> allResults = new ArrayList<>();

        // 并行处理所有网关的响应
        for (Map.Entry<String, DefaultFuture> entry : futureMap.entrySet()) {
            String gateway = entry.getKey();
            DefaultFuture defaultFuture = entry.getValue();

            CompletableFuture<List<DataCodeValDTO>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("开始处理读取命令网关 {} 的响应", gateway);
                    Message message = defaultFuture.get();
                    String playLoad = message.getPlayLoad();
                    JSONObject resultObj = JSONObject.parseObject(playLoad);
                    
                    // 检查是否超时
                    if (resultObj.getInteger("code") == ErrorCode.REQUEST_TIMEOUT.getCode()) {
                        synchronized (timeoutGateways) {
                            timeoutGateways.add(gateway);
                        }
                        // 读取超时：返回空列表，不影响其他网关
                        return timeoutHandlerService.handleReadTimeout(gateway, null);
                    } else {
                        // 正常处理读取响应
                        return processNormalReadResponse(resultObj, gateway);
                    }
                } catch (Exception e) {
                    log.error("处理读取命令网关 {} 响应时发生错误: {}", gateway, e.getMessage());
                    synchronized (timeoutGateways) {
                        timeoutGateways.add(gateway);
                    }
                    // 异常也当作超时处理，返回空列表
                    return timeoutHandlerService.handleReadTimeout(gateway, null);
                }
            });

            futures.add(future);
        }

        // 等待所有网关响应完成
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join(); // 等待所有完成

            // 收集所有结果（包括空结果）
            for (CompletableFuture<List<DataCodeValDTO>> future : futures) {
                List<DataCodeValDTO> gatewayResult = future.get();
                allResults.addAll(gatewayResult);
            }

        } catch (Exception e) {
            log.error("等待读取命令响应时发生错误", e);
        }

        // 检查是否有网关超时，如果有则直接返回空列表（表示读取失败）
        if (!timeoutGateways.isEmpty()) {
            log.error("读取操作失败 - 网关超时: {}", String.join(",", timeoutGateways));
            // 读取操作如果有网关超时，也直接返回空列表
            return new ArrayList<>();
        }

        log.info("读取操作完全成功 - 处理网关数: {}, 获取数据条数: {}",
                futureMap.size(), allResults.size());
        return allResults;
    }

    /**
     * 处理正常的控制响应
     */
    private JSONArray processNormalControlResponse(Long commandId, JSONObject msgResult, String gatewayCode) {
        // 这里应该调用原有的 handlerResult 方法的正常处理逻辑
        // 为了简化，这里返回一个基本的成功响应
        JSONArray resultArray = new JSONArray();
        JSONObject result = new JSONObject();
        result.put("gatewayCode", gatewayCode);
        result.put("commandId", commandId);
        result.put("success", true);
        result.put("msg", "控制命令执行成功");
        resultArray.add(result);
        return resultArray;
    }

    /**
     * 处理正常的读取响应
     */
    private List<DataCodeValDTO> processNormalReadResponse(JSONObject msgResult, String gatewayCode) {
        // 这里应该调用原有的 handlerReadResult 方法的正常处理逻辑
        // 为了简化，这里返回一个空列表，实际应该解析数据
        return new ArrayList<>();
    }
}
