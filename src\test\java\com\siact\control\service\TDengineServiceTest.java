package com.siact.control.service;

import com.siact.control.entity.TDDataEntity;
import com.siact.control.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * TDengineService测试类，用于验证并发插入优化效果
 */
@SpringBootTest
@Slf4j
public class TDengineServiceTest {

    @Autowired
    private TDengineService tdengineService;

    /**
     * 测试缓存队列插入性能
     */
    @Test
    public void testCacheQueueInsert() throws InterruptedException {
        int threadCount = 10;  // 并发线程数
        int batchSize = 10;    // 每批数据量（模拟实际场景的小批量）
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 模拟高频小批量插入
                    for (int j = 0; j < 10; j++) {
                        List<TDDataEntity> testData = generateTestData(batchSize, threadIndex * 10 + j);
                        tdengineService.addToCacheQueue(testData);
                        Thread.sleep(100); // 模拟实际查询间隔
                    }
                    log.info("线程{}完成{}次小批量插入，每次{}条数据", threadIndex, 10, batchSize);
                } catch (Exception e) {
                    log.error("线程{}插入失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();

        // 等待一段时间让定时任务处理完队列中的数据
        Thread.sleep(5000);

        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("缓存队列插入测试完成: {}个线程，每线程10次插入，每次{}条数据，总耗时{}ms",
                threadCount, batchSize, (endTime - startTime));
    }

    /**
     * 测试手动刷新缓存
     */
    @Test
    public void testManualFlush() throws InterruptedException {
        // 添加一些测试数据到缓存队列
        List<TDDataEntity> testData = generateTestData(50, 999);
        tdengineService.addToCacheQueue(testData);

        log.info("已添加{}条测试数据到缓存队列", testData.size());

        // 手动触发刷新
        tdengineService.manualFlushCache();

        log.info("手动刷新测试完成");
    }

    /**
     * 生成测试数据
     */
    private List<TDDataEntity> generateTestData(int count, int threadIndex) {
        List<TDDataEntity> dataList = new ArrayList<>();
        String currentTime = DateUtils.getCurrentimeStr();
        
        for (int i = 0; i < count; i++) {
            TDDataEntity entity = TDDataEntity.builder()
                    .ts(currentTime)
                    .gatewayCode("TEST_GATEWAY_" + threadIndex)
                    .itemid("TEST_DEVICE_" + threadIndex + "_" + i)
                    .devproperty("TEST_PROP_" + threadIndex + "_" + i)
                    .itemvalue(String.valueOf(Math.random() * 100))
                    .build();
            dataList.add(entity);
        }
        
        return dataList;
    }
}
