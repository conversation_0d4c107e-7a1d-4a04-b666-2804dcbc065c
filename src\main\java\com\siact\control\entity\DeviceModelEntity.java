package com.siact.control.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-09 16:16:57
 */
@Data
@TableName("ct_device_model")
public class DeviceModelEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 设备编码
     */
    private String devCode;
    /**
     * 属性编码
     */
    private String devProperty;
    /**
     * 属性短码
     */
    private String propCode;
    /**
     * 点位编码
     */
    private String itemid;
    /**
     * 网关编号
     */
    private String gatewaycode;
    /**
     * 备用字段1
     */
//    private String bak1;
    /**
     * 备用字段2
     */
//    private String bak2;
    /**
     * 点位类型，1-脉冲信号，0-非脉冲信号
     */
    private Integer iotType;
    /**
     * 数据类型，1-开关量，0-模拟量
     */
    private Integer datatype;


}
