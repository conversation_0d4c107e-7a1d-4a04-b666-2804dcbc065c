package com.siact.control.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.siact.control.dao.DeviceModelDao;
import com.siact.control.entity.DeviceModelEntity;
import com.siact.control.service.DeviceModelService;
import com.siact.control.utils.PageUtils;
import com.siact.control.utils.Query;
import com.siact.control.utils.TaskUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service("deviceModelService")
public class DeviceModelServiceImpl extends ServiceImpl<DeviceModelDao, DeviceModelEntity> implements DeviceModelService {
    @Autowired
    DeviceModelDao deviceModelDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<DeviceModelEntity> page = this.page(
                new Query<DeviceModelEntity>().getPage(params),
                new QueryWrapper<DeviceModelEntity>()
        );

        return new PageUtils(page);
    }

    @Override
    public DeviceModelEntity getDeviceModelByPropCode(String propCode) {
        DeviceModelEntity entity= deviceModelDao.getDeviceModelByPropCode(propCode);
        return entity;
    }

    @Override
    public void getAllPropCodeAndIotType() {
        List<DeviceModelEntity> allModelByDevProperty = deviceModelDao.getAllModelByDevProperty();
        if(allModelByDevProperty.size()>0){
            for (DeviceModelEntity deviceModelEntity : allModelByDevProperty) {
                String devproperty = deviceModelEntity.getDevProperty();
                TaskUtil.entityMap.put(devproperty,deviceModelEntity);
            }
        }

    }

    /**
     * 获取充电桩控制的属性短码
     **/

    @Override
    public void getAllModel() {
        List<DeviceModelEntity> allModelByPropCode = deviceModelDao.getAllModelByPropCode();
        if(allModelByPropCode.size()>0){
            for (DeviceModelEntity deviceModelEntity : allModelByPropCode) {
                String propCode = deviceModelEntity.getPropCode();
                TaskUtil.chargeEntityMap.put(propCode,deviceModelEntity);
            }
        }

    }

}