package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 网关锁服务测试类
 * 用于验证并发查询时网关锁机制的效果
 */
@SpringBootTest
@Slf4j
public class RealTimeDataServiceTest {

    @Autowired
    private StrategyService strategyService;

    @Autowired
    private GatewayLockService gatewayLockService;

    /**
     * 测试网关锁机制的并发控制效果
     */
    @Test
    public void testGatewayLockConcurrency() throws InterruptedException {
        int threadCount = 5;  // 并发线程数
        String testGateway = "TEST_GATEWAY_001";  // 使用同一个网关测试锁机制
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    // 使用网关锁执行模拟操作
                    String result = gatewayLockService.executeWithGatewayLock(testGateway, () -> {
                        // 模拟MQTT请求处理时间
                        Thread.sleep(500);
                        return "Thread-" + threadIndex + "-Result";
                    });

                    log.info("线程{}完成网关[{}]操作: {}", threadIndex, testGateway, result);

                } catch (Exception e) {
                    log.error("线程{}执行失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("网关锁并发测试完成: {}个线程访问同一网关，总耗时{}ms",
                threadCount, (endTime - startTime));

        // 验证：由于使用了锁，总耗时应该接近 threadCount * 500ms
        long expectedMinTime = threadCount * 500;
        long actualTime = endTime - startTime;
        log.info("预期最小耗时: {}ms, 实际耗时: {}ms", expectedMinTime, actualTime);
    }

    /**
     * 测试多个网关的并发访问
     */
    @Test
    public void testMultipleGatewaysConcurrency() throws InterruptedException {
        int threadCount = 5;  // 并发线程数
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            final String gatewayCode = "TEST_GATEWAY_" + String.format("%03d", threadIndex);

            executor.submit(() -> {
                try {
                    // 每个线程访问不同的网关，应该可以并行执行
                    String result = gatewayLockService.executeWithGatewayLock(gatewayCode, () -> {
                        // 模拟MQTT请求处理时间
                        Thread.sleep(1000);
                        return "Gateway-" + gatewayCode + "-Result";
                    });

                    log.info("线程{}完成网关[{}]操作: {}", threadIndex, gatewayCode, result);

                } catch (Exception e) {
                    log.error("线程{}执行失败: {}", threadIndex, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        log.info("多网关并发测试完成: {}个线程访问不同网关，总耗时{}ms",
                threadCount, (endTime - startTime));

        // 验证：由于访问不同网关，应该可以并行执行，总耗时应该接近1000ms
        long actualTime = endTime - startTime;
        log.info("实际耗时: {}ms (应该接近1000ms，因为可以并行执行)", actualTime);
    }
}
