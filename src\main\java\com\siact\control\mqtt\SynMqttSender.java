package com.siact.control.mqtt;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.enume.TimeoutTypeEnum;
import com.siact.control.utils.DefaultFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class SynMqttSender {
    @Autowired
    IMqttSender iMqttSender;

    @Value("${mqtt.controlTimeout:5}")
    private int controlTimeout;

    @Value("${mqtt.readTimeout:3}")
    private int readTimeout;

    /**
     * 发送控制命令消息（不带网关编号）
     * 向后兼容的方法
     *
     * @param topic 主题
     * @param msg   消息内容
     * @return DefaultFuture对象
     */
    public DefaultFuture sendMessage(String gatewayCode, String topic, JSONObject msg) {

        iMqttSender.sendToMqtt(topic, 0, JSON.toJSONString(msg));

        log.info("发送控制命令 - 网关: {}, 主题: {}, 命令ID: {}, 命令内容: {}",
                gatewayCode, topic, msg.getLong("cid"), msg);
        DefaultFuture future = new DefaultFuture(msg.getLong("cid"), gatewayCode, controlTimeout, TimeoutTypeEnum.CONTROL_TIMEOUT);
        return future;

    }


    /**
     * 发送读取命令消息（不带网关编号）
     * 向后兼容的方法
     *
     * @param issueTopic 主题
     * @param msg        消息内容
     * @return DefaultFuture对象
     */
    public DefaultFuture sendReadMessage(String gatewayCode, String issueTopic, JSONObject msg) {

        iMqttSender.sendToMqtt(issueTopic, 0, JSON.toJSONString(msg));
        log.info("发送读取命令 - 网关: {}, 主题: {}, 命令内容: {}", gatewayCode, issueTopic, msg);
        DefaultFuture future = new DefaultFuture(99999L, gatewayCode, readTimeout, TimeoutTypeEnum.READ_TIMEOUT);
        return future;

    }

}
