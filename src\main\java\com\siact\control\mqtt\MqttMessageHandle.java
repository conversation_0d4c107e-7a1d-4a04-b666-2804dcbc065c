package com.siact.control.mqtt;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Package com.siact.control.mqtt
 * @description:
 * @create 2023/12/15 18:31
 */
@Component
@Slf4j
public class MqttMessageHandle implements MessageHandler {
    @Autowired
    MqttProperties mqttProperties;
    @Autowired
    MqttResHandler mqttResHandler;

    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        String topic = message.getHeaders().get("mqtt_receivedTopic").toString();

        if (topic.endsWith("/result")) {
            String msg = message.getPayload().toString();

            try {
                JSONObject msgObj = JSONObject.parseObject(msg);
                Integer cmd = msgObj.getInteger("cmd");
                // 从主题中提取网关编号
                String gatewayCode = extractGatewayCodeFromTopic(topic);

                //cmd=20是控制命令的返回消息
                if (cmd == 20) {
                    Long cid = msgObj.getLong("cid");
                    log.info("接收控制响应 - 网关: {}, 主题: {}, 命令ID: {}", gatewayCode, topic, cid);
                    com.siact.control.mqtt.Message mes = new com.siact.control.mqtt.Message();
                    mes.setMessageId(cid);
                    mes.setPlayLoad(msg);
                    mes.setGatewayCode(gatewayCode);
                    mqttResHandler.deal(mes);

                } else if (cmd == 11) {
                    log.info("接收读取响应 - 网关: {}, 主题: {}", gatewayCode, topic);
                    //数据读取
                    com.siact.control.mqtt.Message mes = new com.siact.control.mqtt.Message();
                    mes.setMessageId(99999L);
                    mes.setPlayLoad(msg);
                    mes.setGatewayCode(gatewayCode);
                    mqttResHandler.deal(mes);

                } else {
                    log.error("未知命令类型 - cmd: {}, 网关: {}", cmd, gatewayCode);
                }
            } catch (Exception e) {
                log.error("处理MQTT消息异常 - 主题: {}, 错误: {}", topic, e.getMessage());
            }

          /*  log.info("\n--------------------START-------------------\n" +
                    "接收到订阅消息:\ntopic:" + topic + "\nmessage:" + msg +
                    "\n---------------------END--------------------");*/
        }
        //  log.info("接收到消息===="+message);
    }

    /**
     * 从主题中提取网关编号
     *
     * @param topic MQTT主题
     * @return 网关编号，如果无法提取则返回null
     */
    private String extractGatewayCodeFromTopic(String topic) {
        try {
            // 预期主题格式: /ARM/网关编号/版本/result
            // 或者类似格式
            String[] parts = topic.split("/");
            if (parts.length >= 3) {
                return parts[2]; // 网关编号通常是第三部分
            }
        } catch (Exception e) {
            log.warn("无法从主题 {} 中提取网关编号: {}", topic, e.getMessage());
        }
        return null;
    }
}
