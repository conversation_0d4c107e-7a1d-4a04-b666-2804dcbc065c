package com.siact.control.utils;


import com.alibaba.fastjson.JSONObject;
import com.siact.control.entity.DeviceModelEntity;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Package com.siact.control.utils
 * @description: 全局变量
 * @create 2023/4/10 9:10
 */
public class TaskUtil {
    //存放网关+点位对应的短码对象
    public static ConcurrentHashMap<String, String> iotPropMap=new ConcurrentHashMap();
    public static ConcurrentHashMap<String, String> iotReadPropMap=new ConcurrentHashMap();
    
    //存放(命令id,(网关编号，List[itemid]))
    public static ConcurrentHashMap<Long, Map<String, List<String>>> commandGatewayItemsMap = new ConcurrentHashMap<>();
    
    // 反向映射，根据网关编号和itemid查找关联的命令ID
    public static ConcurrentHashMap<String, Long> gatewayItemCommandMap = new ConcurrentHashMap<>();

    //存放充电桩的实体类(propcode,DeviceModelEntity)
    public static ConcurrentHashMap<String, DeviceModelEntity> chargeEntityMap=new ConcurrentHashMap();
    //存放非充电桩模型的实体类(devproperty,DeviceModelEntity)
    public static ConcurrentHashMap<String, DeviceModelEntity> entityMap=new ConcurrentHashMap();

    /**
     * 保存命令ID与网关点位的映射关系
     * @param commandId 命令ID
     * @param gatewayItemsMap 网关编号与点位列表的映射
     */
    public static void saveCommandGatewayItems(Long commandId, Map<String, List<String>> gatewayItemsMap) {
        // 存储命令与网关点位的映射
        commandGatewayItemsMap.put(commandId, gatewayItemsMap);
        
        // 同时建立反向映射，便于根据网关和点位查找命令
        for (Map.Entry<String, List<String>> entry : gatewayItemsMap.entrySet()) {
            String gateway = entry.getKey();
            List<String> items = entry.getValue();
            
            for (String item : items) {
                gatewayItemCommandMap.put(gateway + "_" + item, commandId);
            }
        }
    }
    
    /**
     * 获取特定命令ID对应的网关点位映射
     * @param commandId 命令ID
     * @return 网关编号与点位列表的映射
     */
    public static Map<String, List<String>> getCommandGatewayItems(Long commandId) {
        return commandGatewayItemsMap.get(commandId);
    }
    
    /**
     * 获取特定命令ID和网关编号对应的点位列表
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     * @return 点位ID列表，如果不存在则返回空列表
     */
    public static List<String> getItemsByCommandIdAndGateway(Long commandId, String gatewayCode) {
        if (commandId == null || gatewayCode == null) {
            return Collections.emptyList();
        }
        
        Map<String, List<String>> gatewayItemsMap = commandGatewayItemsMap.get(commandId);
        if (gatewayItemsMap == null) {
            return Collections.emptyList();
        }
        
        List<String> itemIds = gatewayItemsMap.get(gatewayCode);
        return itemIds != null ? new ArrayList<>(itemIds) : Collections.emptyList();
    }
    
    /**
     * 清除特定命令ID的映射关系
     * @param commandId 命令ID
     */
    public static void removeCommandGatewayItems(Long commandId) {
        Map<String, List<String>> gatewayItemsMap = commandGatewayItemsMap.remove(commandId);
        if (gatewayItemsMap != null) {
            // 同时清除反向映射
            for (Map.Entry<String, List<String>> entry : gatewayItemsMap.entrySet()) {
                String gateway = entry.getKey();
                List<String> items = entry.getValue();
                
                for (String item : items) {
                    gatewayItemCommandMap.remove(gateway + "_" + item);
                }
            }
        }
    }
    
    /**
     * 根据网关编号和点位ID查找关联的命令ID
     * @param gateway 网关编号
     * @param itemId 点位ID
     * @return 关联的命令ID，如果不存在则返回null
     */
    public static Long getCommandIdByGatewayItem(String gateway, String itemId) {
        return gatewayItemCommandMap.get(gateway + "_" + itemId);
    }
    
    /**
     * 根据命令ID和网关编号清除特定网关的映射关系
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     * @return 是否成功清除映射关系
     */
    public static boolean removeCommandGatewayItemsByGateway(Long commandId, String gatewayCode) {
        if (commandId == null || gatewayCode == null) {
            return false;
        }
        
        // 获取该命令ID对应的网关点位映射
        Map<String, List<String>> gatewayItemsMap = commandGatewayItemsMap.get(commandId);
        if (gatewayItemsMap == null) {
            return false;
        }
        
        // 获取该网关下的点位列表
        List<String> itemIds = gatewayItemsMap.remove(gatewayCode);
        
        // 如果已经没有网关了，则完全移除该命令ID
        if (gatewayItemsMap.isEmpty()) {
            commandGatewayItemsMap.remove(commandId);
        }
        
        // 如果该网关存在点位，清除反向映射
        if (itemIds != null) {
            for (String itemId : itemIds) {
                gatewayItemCommandMap.remove(gatewayCode + "_" + itemId);
            }
            return true;
        }
        
        return false;
    }
}
