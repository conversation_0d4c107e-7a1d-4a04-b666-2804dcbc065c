package com.siact.control.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 17:14:20
 */
@Data
@TableName("ct_command_detail")
public class CommandDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 下发时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date issueTime;
	/**
	 * 命令详情
	 */
	private Integer controlType;
	/**
	 * 命令详情
	 */
	private String issueDetail;
	/**
	 * 创建用户
	 */
	private String createBy;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 命令状态
	 */
	private String commandStatus;
	/**
	 * 命令是否执行完毕的标志位，0-执行完毕，1-正在执行
	 */
	private Integer endFlag;




}
